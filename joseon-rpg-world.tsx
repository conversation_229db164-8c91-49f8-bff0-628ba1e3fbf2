"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Button } from "@/components/ui/button"

interface Vector2 {
  x: number
  y: number
}

interface Camera {
  x: number
  y: number
  targetX: number
  targetY: number
}

interface Character {
  pos: Vector2
  vel: Vector2
  size: Vector2
  health: number
  maxHealth: number
  mana: number
  maxMana: number
  level: number
  exp: number
  expToNext: number
  stats: {
    strength: number
    agility: number
    vitality: number
    intelligence: number
  }
  state: "idle" | "walking" | "attacking" | "talking"
  direction: number
  animFrame: number
  animTimer: number
  attackTimer: number
  weapon: "sword" | "bow" | "spear"
  inventory: InventoryItem[]
  gold: number
  currentQuest: Quest | null
}

interface InventoryItem {
  id: string
  name: string
  type: "weapon" | "armor" | "potion" | "misc"
  quantity: number
  description: string
}

interface Quest {
  id: string
  title: string
  description: string
  objectives: QuestObjective[]
  reward: {
    exp: number
    gold: number
    items?: InventoryItem[]
  }
  completed: boolean
}

interface QuestObjective {
  type: "kill" | "collect" | "talk" | "explore"
  target: string
  current: number
  required: number
  completed: boolean
}

interface NPC {
  id: string
  pos: Vector2
  size: Vector2
  name: string
  type: "villager" | "merchant" | "guard" | "elder"
  dialogue: string[]
  currentDialogue: number
  quests: Quest[]
  shop?: ShopItem[]
}

interface ShopItem {
  item: InventoryItem
  price: number
}

interface Enemy {
  id: number
  pos: Vector2
  vel: Vector2
  size: Vector2
  health: number
  maxHealth: number
  type: "bandit" | "wildBoar" | "tiger" | "ninja" | "goblin"
  state: "idle" | "patrolling" | "chasing" | "attacking" | "hit" | "dying"
  direction: number
  animFrame: number
  animTimer: number
  hitTimer: number
  deathTimer: number
  knockback: Vector2
  attackCooldown: number
  expReward: number
  patrolCenter: Vector2
  patrolRadius: number
  aggroRange: number
}

interface WorldTile {
  type: "grass" | "water" | "mountain" | "forest" | "village" | "road" | "dungeon"
  walkable: boolean
  color: string
}

interface Building {
  pos: Vector2
  size: Vector2
  type: "house" | "shop" | "temple" | "inn"
  name: string
  npcs: string[]
}

interface Particle {
  pos: Vector2
  vel: Vector2
  life: number
  maxLife: number
  color: string
  size: number
  type: "spark" | "leaf" | "coin" | "magic"
}

export default function JoseonRPGWorld() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const keysRef = useRef<Set<string>>(new Set())
  const lastTimeRef = useRef<number>(0)

  const [gameState, setGameState] = useState<"playing" | "dialogue" | "inventory" | "shop" | "gameOver">("playing")
  const [currentNPC, setCurrentNPC] = useState<NPC | null>(null)
  const [showInventory, setShowInventory] = useState(false)
  const [canvasSize, setCanvasSize] = useState({ width: 1200, height: 800 })

  // 월드 크기 (매우 큰 맵)
  const WORLD_WIDTH = 200
  const WORLD_HEIGHT = 200
  const TILE_SIZE = 32

  // 카메라
  const cameraRef = useRef<Camera>({
    x: 0,
    y: 0,
    targetX: 0,
    targetY: 0,
  })

  // 캐릭터
  const characterRef = useRef<Character>({
    pos: { x: (WORLD_WIDTH * TILE_SIZE) / 2, y: (WORLD_HEIGHT * TILE_SIZE) / 2 },
    vel: { x: 0, y: 0 },
    size: { x: 28, y: 32 },
    health: 100,
    maxHealth: 100,
    mana: 50,
    maxMana: 50,
    level: 1,
    exp: 0,
    expToNext: 100,
    stats: {
      strength: 10,
      agility: 8,
      vitality: 12,
      intelligence: 6,
    },
    state: "idle",
    direction: 1,
    animFrame: 0,
    animTimer: 0,
    attackTimer: 0,
    weapon: "sword",
    inventory: [
      { id: "sword1", name: "철검", type: "weapon", quantity: 1, description: "튼튼한 철로 만든 검" },
      { id: "potion1", name: "체력 물약", type: "potion", quantity: 3, description: "체력을 회복하는 물약" },
    ],
    gold: 50,
    currentQuest: null,
  })

  // 게임 오브젝트들
  const enemiesRef = useRef<Enemy[]>([])
  const npcsRef = useRef<NPC[]>([])
  const buildingsRef = useRef<Building[]>([])
  const particlesRef = useRef<Particle[]>([])

  // 월드 맵 생성
  const worldMapRef = useRef<WorldTile[][]>([])

  // 전통 색상
  const colors = {
    grass: "#4a5d23",
    water: "#2e5984",
    mountain: "#8b7355",
    forest: "#2d4a22",
    village: "#d2b48c",
    road: "#8b7d6b",
    character: "#dc143c",
    npc: "#4169e1",
    enemy: "#8b0000",
    ui: "#f4e4bc",
    text: "#2f1b14",
  }

  // 화면 크기 조정
  useEffect(() => {
    const updateCanvasSize = () => {
      setCanvasSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    updateCanvasSize()
    window.addEventListener("resize", updateCanvasSize)
    return () => window.removeEventListener("resize", updateCanvasSize)
  }, [])

  // 월드 맵 생성 함수
  const generateWorld = useCallback(() => {
    const world: WorldTile[][] = []

    for (let y = 0; y < WORLD_HEIGHT; y++) {
      world[y] = []
      for (let x = 0; x < WORLD_WIDTH; x++) {
        let tileType: WorldTile["type"] = "grass"

        // 노이즈 기반 지형 생성
        const noise = Math.sin(x * 0.1) * Math.cos(y * 0.1) + Math.random() * 0.5

        if (noise > 0.7) {
          tileType = "mountain"
        } else if (noise > 0.4) {
          tileType = "forest"
        } else if (noise < -0.3) {
          tileType = "water"
        }

        // 마을 지역 (중앙 근처)
        const centerX = WORLD_WIDTH / 2
        const centerY = WORLD_HEIGHT / 2
        const distFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2)

        if (distFromCenter < 15 && Math.random() > 0.7) {
          tileType = "village"
        }

        // 길 생성 (마을 주변)
        if (distFromCenter < 25 && Math.random() > 0.95) {
          tileType = "road"
        }

        world[y][x] = {
          type: tileType,
          walkable: tileType !== "water" && tileType !== "mountain",
          color: colors[tileType],
        }
      }
    }

    worldMapRef.current = world
  }, [])

  // NPC 생성
  const generateNPCs = useCallback(() => {
    const npcs: NPC[] = []

    // 마을 장로
    npcs.push({
      id: "elder1",
      pos: { x: (WORLD_WIDTH * TILE_SIZE) / 2 - 100, y: (WORLD_HEIGHT * TILE_SIZE) / 2 - 50 },
      size: { x: 28, y: 32 },
      name: "마을 장로",
      type: "elder",
      dialogue: ["어서 오시게, 젊은 무사여.", "이 마을에 도적들이 나타나고 있다네.", "마을을 지켜주면 보상을 주겠네."],
      currentDialogue: 0,
      quests: [
        {
          id: "quest1",
          title: "도적 소탕",
          description: "마을 주변의 도적 5명을 처치하시오",
          objectives: [
            {
              type: "kill",
              target: "bandit",
              current: 0,
              required: 5,
              completed: false,
            },
          ],
          reward: {
            exp: 100,
            gold: 100,
            items: [{ id: "sword2", name: "강철검", type: "weapon", quantity: 1, description: "더욱 날카로운 강철검" }],
          },
          completed: false,
        },
      ],
    })

    // 상인
    npcs.push({
      id: "merchant1",
      pos: { x: (WORLD_WIDTH * TILE_SIZE) / 2 + 80, y: (WORLD_HEIGHT * TILE_SIZE) / 2 + 30 },
      size: { x: 28, y: 32 },
      name: "무기 상인",
      type: "merchant",
      dialogue: ["좋은 무기가 필요하신가요?", "최고 품질의 무기를 판매합니다!"],
      currentDialogue: 0,
      quests: [],
      shop: [
        { item: { id: "sword3", name: "명검", type: "weapon", quantity: 1, description: "전설의 명검" }, price: 500 },
        { item: { id: "bow1", name: "강궁", type: "weapon", quantity: 1, description: "강력한 활" }, price: 300 },
        {
          item: { id: "potion2", name: "대형 체력 물약", type: "potion", quantity: 1, description: "많은 체력을 회복" },
          price: 50,
        },
      ],
    })

    npcsRef.current = npcs
  }, [])

  // 적 생성
  const spawnEnemies = useCallback(() => {
    const character = characterRef.current
    const enemies = enemiesRef.current

    // 화면 밖에서 적 생성
    if (enemies.length < 10 && Math.random() < 0.02) {
      const camera = cameraRef.current
      const spawnDistance = 400
      const angle = Math.random() * Math.PI * 2

      const enemy: Enemy = {
        id: Date.now() + Math.random(),
        pos: {
          x: character.pos.x + Math.cos(angle) * spawnDistance,
          y: character.pos.y + Math.sin(angle) * spawnDistance,
        },
        vel: { x: 0, y: 0 },
        size: { x: 28, y: 28 },
        health: 3,
        maxHealth: 3,
        type: Math.random() > 0.5 ? "bandit" : "goblin",
        state: "patrolling",
        direction: 1,
        animFrame: 0,
        animTimer: 0,
        hitTimer: 0,
        deathTimer: 0,
        knockback: { x: 0, y: 0 },
        attackCooldown: 0,
        expReward: 20,
        patrolCenter: { x: 0, y: 0 },
        patrolRadius: 100,
        aggroRange: 80,
      }

      enemy.patrolCenter = { ...enemy.pos }
      enemies.push(enemy)
    }
  }, [])

  // 사운드 효과
  const playSound = useCallback(
    (frequency: number, duration: number, type: "sine" | "square" | "sawtooth" = "sine", volume = 0.1) => {
      try {
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        const oscillator = audioContext.createOscillator()
        const gainNode = audioContext.createGain()

        oscillator.connect(gainNode)
        gainNode.connect(audioContext.destination)

        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime)
        oscillator.type = type

        gainNode.gain.setValueAtTime(volume, audioContext.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration)

        oscillator.start(audioContext.currentTime)
        oscillator.stop(audioContext.currentTime + duration)
      } catch (e) {
        // 사운드 실패 시 무시
      }
    },
    [],
  )

  // 키보드 입력
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.key.toLowerCase())

      if (e.key === "i" || e.key === "I") {
        setShowInventory(!showInventory)
      }

      if (e.key === "Escape") {
        setGameState("playing")
        setCurrentNPC(null)
        setShowInventory(false)
      }

      if (e.key === " " && gameState === "dialogue" && currentNPC) {
        // 대화 진행
        if (currentNPC.currentDialogue < currentNPC.dialogue.length - 1) {
          currentNPC.currentDialogue++
        } else {
          setGameState("playing")
          setCurrentNPC(null)
        }
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.key.toLowerCase())
    }

    window.addEventListener("keydown", handleKeyDown)
    window.addEventListener("keyup", handleKeyUp)

    return () => {
      window.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("keyup", handleKeyUp)
    }
  }, [showInventory, gameState, currentNPC])

  // 충돌 감지
  const checkCollision = useCallback((a: any, b: any) => {
    return (
      a.pos.x < b.pos.x + b.size.x &&
      a.pos.x + a.size.x > b.pos.x &&
      a.pos.y < b.pos.y + b.size.y &&
      a.pos.y + a.size.y > b.pos.y
    )
  }, [])

  // 타일 충돌 체크
  const checkTileCollision = useCallback((x: number, y: number) => {
    const tileX = Math.floor(x / TILE_SIZE)
    const tileY = Math.floor(y / TILE_SIZE)

    if (tileX < 0 || tileX >= WORLD_WIDTH || tileY < 0 || tileY >= WORLD_HEIGHT) {
      return false
    }

    return worldMapRef.current[tileY][tileX].walkable
  }, [])

  // 게임 업데이트
  const updateGame = useCallback(
    (deltaTime: number) => {
      if (gameState !== "playing") return

      const character = characterRef.current
      const keys = keysRef.current
      const camera = cameraRef.current

      // 캐릭터 이동
      character.vel.x *= 0.8
      character.vel.y *= 0.8

      const speed = 3
      let moving = false

      if (keys.has("a") || keys.has("arrowleft")) {
        character.vel.x = -speed
        character.direction = -1
        moving = true
      }
      if (keys.has("d") || keys.has("arrowright")) {
        character.vel.x = speed
        character.direction = 1
        moving = true
      }
      if (keys.has("w") || keys.has("arrowup")) {
        character.vel.y = -speed
        moving = true
      }
      if (keys.has("s") || keys.has("arrowdown")) {
        character.vel.y = speed
        moving = true
      }

      // 상태 업데이트
      if (moving) {
        character.state = "walking"
      } else {
        character.state = "idle"
      }

      // 이동 전 위치 저장
      const newX = character.pos.x + character.vel.x
      const newY = character.pos.y + character.vel.y

      // 타일 충돌 체크
      if (
        checkTileCollision(newX, character.pos.y) &&
        checkTileCollision(newX + character.size.x, character.pos.y) &&
        checkTileCollision(newX, character.pos.y + character.size.y) &&
        checkTileCollision(newX + character.size.x, character.pos.y + character.size.y)
      ) {
        character.pos.x = newX
      }

      if (
        checkTileCollision(character.pos.x, newY) &&
        checkTileCollision(character.pos.x + character.size.x, newY) &&
        checkTileCollision(character.pos.x, newY + character.size.y) &&
        checkTileCollision(character.pos.x + character.size.x, newY + character.size.y)
      ) {
        character.pos.y = newY
      }

      // 월드 경계
      character.pos.x = Math.max(0, Math.min(WORLD_WIDTH * TILE_SIZE - character.size.x, character.pos.x))
      character.pos.y = Math.max(0, Math.min(WORLD_HEIGHT * TILE_SIZE - character.size.y, character.pos.y))

      // 카메라 업데이트 (부드러운 추적)
      camera.targetX = character.pos.x - canvasSize.width / 2
      camera.targetY = character.pos.y - canvasSize.height / 2

      camera.x += (camera.targetX - camera.x) * 0.1
      camera.y += (camera.targetY - camera.y) * 0.1

      // NPC 상호작용 체크
      if (keys.has("e")) {
        npcsRef.current.forEach((npc) => {
          const distance = Math.sqrt((character.pos.x - npc.pos.x) ** 2 + (character.pos.y - npc.pos.y) ** 2)

          if (distance < 50) {
            setCurrentNPC(npc)
            setGameState("dialogue")
            npc.currentDialogue = 0
          }
        })
      }

      // 적 업데이트
      enemiesRef.current.forEach((enemy, index) => {
        if (enemy.state === "dying") {
          enemy.deathTimer++
          if (enemy.deathTimer > 60) {
            enemiesRef.current.splice(index, 1)
          }
          return
        }

        const distToPlayer = Math.sqrt((character.pos.x - enemy.pos.x) ** 2 + (character.pos.y - enemy.pos.y) ** 2)

        if (distToPlayer < enemy.aggroRange && enemy.state !== "chasing") {
          enemy.state = "chasing"
        }

        if (enemy.state === "chasing") {
          const dx = character.pos.x - enemy.pos.x
          const dy = character.pos.y - enemy.pos.y
          const dist = Math.sqrt(dx * dx + dy * dy)

          if (dist > 0) {
            enemy.vel.x = (dx / dist) * 1.5
            enemy.vel.y = (dy / dist) * 1.5
            enemy.direction = dx > 0 ? 1 : -1
          }
        } else if (enemy.state === "patrolling") {
          // 순찰 AI
          const dx = enemy.patrolCenter.x - enemy.pos.x
          const dy = enemy.patrolCenter.y - enemy.pos.y
          const distFromCenter = Math.sqrt(dx * dx + dy * dy)

          if (distFromCenter > enemy.patrolRadius) {
            enemy.vel.x = (dx / distFromCenter) * 0.5
            enemy.vel.y = (dy / distFromCenter) * 0.5
          } else {
            enemy.vel.x += (Math.random() - 0.5) * 0.2
            enemy.vel.y += (Math.random() - 0.5) * 0.2
          }
        }

        // 적 이동
        const enemyNewX = enemy.pos.x + enemy.vel.x
        const enemyNewY = enemy.pos.y + enemy.vel.y

        if (checkTileCollision(enemyNewX, enemy.pos.y)) {
          enemy.pos.x = enemyNewX
        }
        if (checkTileCollision(enemy.pos.x, enemyNewY)) {
          enemy.pos.y = enemyNewY
        }

        // 애니메이션
        enemy.animTimer++
        if (enemy.animTimer > 15) {
          enemy.animFrame = (enemy.animFrame + 1) % 3
          enemy.animTimer = 0
        }
      })

      // 적 스폰
      spawnEnemies()

      // 애니메이션
      character.animTimer++
      if (character.animTimer > 10) {
        character.animFrame = (character.animFrame + 1) % 4
        character.animTimer = 0
      }
    },
    [gameState, canvasSize, checkTileCollision, checkCollision, spawnEnemies],
  )

  // 렌더링
  const render = useCallback(() => {
    const canvas = canvasRef.current
    const ctx = canvas?.getContext("2d")
    if (!canvas || !ctx) return

    const character = characterRef.current
    const camera = cameraRef.current

    // 화면 클리어
    ctx.fillStyle = "#87CEEB"
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 월드 렌더링 (카메라 기준)
    const startTileX = Math.max(0, Math.floor(camera.x / TILE_SIZE))
    const endTileX = Math.min(WORLD_WIDTH, Math.ceil((camera.x + canvas.width) / TILE_SIZE))
    const startTileY = Math.max(0, Math.floor(camera.y / TILE_SIZE))
    const endTileY = Math.min(WORLD_HEIGHT, Math.ceil((camera.y + canvas.height) / TILE_SIZE))

    for (let y = startTileY; y < endTileY; y++) {
      for (let x = startTileX; x < endTileX; x++) {
        const tile = worldMapRef.current[y][x]
        const screenX = x * TILE_SIZE - camera.x
        const screenY = y * TILE_SIZE - camera.y

        ctx.fillStyle = tile.color
        ctx.fillRect(screenX, screenY, TILE_SIZE, TILE_SIZE)

        // 타일 디테일
        if (tile.type === "forest") {
          ctx.fillStyle = "#1a3d1a"
          ctx.fillRect(screenX + 8, screenY + 8, 16, 16)
        } else if (tile.type === "village") {
          ctx.fillStyle = "#8b4513"
          ctx.fillRect(screenX + 4, screenY + 4, 24, 24)
        }
      }
    }

    // NPC 렌더링
    npcsRef.current.forEach((npc) => {
      const screenX = npc.pos.x - camera.x
      const screenY = npc.pos.y - camera.y

      // 화면에 보이는지 체크
      if (screenX > -50 && screenX < canvas.width + 50 && screenY > -50 && screenY < canvas.height + 50) {
        ctx.fillStyle = colors.npc
        ctx.fillRect(screenX, screenY, npc.size.x, npc.size.y)

        // NPC 타입별 디테일
        ctx.fillStyle = "#FDBCB4"
        ctx.fillRect(screenX + 6, screenY + 4, 16, 12)

        if (npc.type === "elder") {
          // 장로 수염
          ctx.fillStyle = "#FFFFFF"
          ctx.fillRect(screenX + 8, screenY + 12, 12, 4)
        } else if (npc.type === "merchant") {
          // 상인 모자
          ctx.fillStyle = "#8B4513"
          ctx.fillRect(screenX + 4, screenY, 20, 8)
        }

        // 이름 표시
        ctx.fillStyle = "#FFFFFF"
        ctx.font = "12px serif"
        ctx.textAlign = "center"
        ctx.fillText(npc.name, screenX + npc.size.x / 2, screenY - 5)

        // 상호작용 가능 표시
        const distToPlayer = Math.sqrt((character.pos.x - npc.pos.x) ** 2 + (character.pos.y - npc.pos.y) ** 2)
        if (distToPlayer < 50) {
          ctx.fillStyle = "#FFD700"
          ctx.font = "10px serif"
          ctx.fillText("E키로 대화", screenX + npc.size.x / 2, screenY - 15)
        }
      }
    })

    // 적 렌더링
    enemiesRef.current.forEach((enemy) => {
      const screenX = enemy.pos.x - camera.x
      const screenY = enemy.pos.y - camera.y

      if (screenX > -50 && screenX < canvas.width + 50 && screenY > -50 && screenY < canvas.height + 50) {
        let enemyColor = colors.enemy
        if (enemy.state === "hit") enemyColor = "#FFFFFF"

        ctx.fillStyle = enemyColor
        ctx.fillRect(screenX, screenY, enemy.size.x, enemy.size.y)

        // 적 타입별 디테일
        if (enemy.type === "bandit") {
          ctx.fillStyle = "#2F1B14"
          ctx.fillRect(screenX + 4, screenY, 20, 8)
        } else if (enemy.type === "goblin") {
          ctx.fillStyle = "#228B22"
          ctx.fillRect(screenX + 6, screenY + 4, 16, 12)
        }

        // 체력바
        if (enemy.health < enemy.maxHealth) {
          ctx.fillStyle = "rgba(0, 0, 0, 0.7)"
          ctx.fillRect(screenX, screenY - 8, enemy.size.x, 4)
          ctx.fillStyle = "#FF0000"
          ctx.fillRect(screenX, screenY - 8, (enemy.health / enemy.maxHealth) * enemy.size.x, 4)
        }
      }
    })

    // 캐릭터 렌더링
    const screenX = character.pos.x - camera.x
    const screenY = character.pos.y - camera.y

    ctx.save()
    ctx.translate(screenX + character.size.x / 2, screenY + character.size.y / 2)
    if (character.direction < 0) ctx.scale(-1, 1)

    // 한복
    ctx.fillStyle = colors.character
    ctx.fillRect(-character.size.x / 2, -character.size.y / 2, character.size.x, character.size.y)

    // 갓
    ctx.fillStyle = "#2F1B14"
    ctx.fillRect(-character.size.x / 2 - 2, -character.size.y / 2 - 6, character.size.x + 4, 6)

    // 얼굴
    ctx.fillStyle = "#FDBCB4"
    ctx.fillRect(-8, -10, 16, 10)

    // 눈
    ctx.fillStyle = "#000000"
    ctx.fillRect(2, -6, 2, 2)

    ctx.restore()

    // UI 렌더링
    // 상태창
    ctx.fillStyle = "rgba(244, 228, 188, 0.95)"
    ctx.fillRect(10, 10, 300, 140)
    ctx.strokeStyle = colors.text
    ctx.lineWidth = 2
    ctx.strokeRect(10, 10, 300, 140)

    ctx.fillStyle = colors.text
    ctx.font = "bold 16px serif"
    ctx.textAlign = "left"
    ctx.fillText(`${character.level}급 무사`, 20, 30)

    ctx.font = "12px serif"
    ctx.fillText(`체력: ${Math.floor(character.health)}/${character.maxHealth}`, 20, 50)
    ctx.fillText(`기력: ${Math.floor(character.mana)}/${character.maxMana}`, 20, 70)
    ctx.fillText(`경험치: ${character.exp}/${character.expToNext}`, 20, 90)
    ctx.fillText(`황금: ${character.gold}냥`, 20, 110)
    ctx.fillText(
      `위치: (${Math.floor(character.pos.x / TILE_SIZE)}, ${Math.floor(character.pos.y / TILE_SIZE)})`,
      20,
      130,
    )

    // 미니맵
    const minimapSize = 150
    const minimapX = canvas.width - minimapSize - 10
    const minimapY = 10

    ctx.fillStyle = "rgba(0, 0, 0, 0.7)"
    ctx.fillRect(minimapX, minimapY, minimapSize, minimapSize)
    ctx.strokeStyle = "#FFFFFF"
    ctx.strokeRect(minimapX, minimapY, minimapSize, minimapSize)

    // 미니맵 타일들
    const minimapScale = minimapSize / (WORLD_WIDTH * TILE_SIZE)
    for (let y = 0; y < WORLD_HEIGHT; y += 4) {
      for (let x = 0; x < WORLD_WIDTH; x += 4) {
        const tile = worldMapRef.current[y][x]
        ctx.fillStyle = tile.color
        ctx.fillRect(
          minimapX + x * TILE_SIZE * minimapScale,
          minimapY + y * TILE_SIZE * minimapScale,
          4 * TILE_SIZE * minimapScale,
          4 * TILE_SIZE * minimapScale,
        )
      }
    }

    // 미니맵 플레이어 위치
    ctx.fillStyle = "#FF0000"
    ctx.fillRect(minimapX + character.pos.x * minimapScale - 2, minimapY + character.pos.y * minimapScale - 2, 4, 4)

    // 조작법
    ctx.fillStyle = "rgba(244, 228, 188, 0.9)"
    ctx.font = "12px serif"
    ctx.fillText("WASD: 이동 | E: 상호작용 | I: 인벤토리", 10, canvas.height - 10)

    // 대화창
    if (gameState === "dialogue" && currentNPC) {
      const dialogueHeight = 120
      const dialogueY = canvas.height - dialogueHeight - 10

      ctx.fillStyle = "rgba(244, 228, 188, 0.95)"
      ctx.fillRect(10, dialogueY, canvas.width - 20, dialogueHeight)
      ctx.strokeStyle = colors.text
      ctx.lineWidth = 3
      ctx.strokeRect(10, dialogueY, canvas.width - 20, dialogueHeight)

      ctx.fillStyle = colors.text
      ctx.font = "bold 16px serif"
      ctx.fillText(currentNPC.name, 20, dialogueY + 25)

      ctx.font = "14px serif"
      const dialogue = currentNPC.dialogue[currentNPC.currentDialogue]
      ctx.fillText(dialogue, 20, dialogueY + 50)

      ctx.font = "12px serif"
      ctx.fillText("스페이스바로 계속...", 20, dialogueY + 100)
    }

    // 인벤토리
    if (showInventory) {
      const invWidth = 400
      const invHeight = 300
      const invX = (canvas.width - invWidth) / 2
      const invY = (canvas.height - invHeight) / 2

      ctx.fillStyle = "rgba(244, 228, 188, 0.95)"
      ctx.fillRect(invX, invY, invWidth, invHeight)
      ctx.strokeStyle = colors.text
      ctx.lineWidth = 3
      ctx.strokeRect(invX, invY, invWidth, invHeight)

      ctx.fillStyle = colors.text
      ctx.font = "bold 18px serif"
      ctx.textAlign = "center"
      ctx.fillText("인벤토리", invX + invWidth / 2, invY + 30)

      ctx.font = "14px serif"
      ctx.textAlign = "left"
      character.inventory.forEach((item, index) => {
        const itemY = invY + 60 + index * 25
        ctx.fillText(`${item.name} x${item.quantity}`, invX + 20, itemY)
        ctx.font = "10px serif"
        ctx.fillStyle = "#666"
        ctx.fillText(item.description, invX + 20, itemY + 15)
        ctx.fillStyle = colors.text
        ctx.font = "14px serif"
      })

      ctx.font = "12px serif"
      ctx.textAlign = "center"
      ctx.fillText("ESC로 닫기", invX + invWidth / 2, invY + invHeight - 20)
    }
  }, [gameState, currentNPC, showInventory, canvasSize, colors])

  // 게임 루프
  useEffect(() => {
    const gameLoop = (currentTime: number) => {
      const deltaTime = currentTime - lastTimeRef.current
      lastTimeRef.current = currentTime

      updateGame(deltaTime)
      render()

      animationRef.current = requestAnimationFrame(gameLoop)
    }

    animationRef.current = requestAnimationFrame(gameLoop)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [updateGame, render])

  // 초기화
  useEffect(() => {
    generateWorld()
    generateNPCs()
  }, [generateWorld, generateNPCs])

  // 캔버스 크기 업데이트
  useEffect(() => {
    const canvas = canvasRef.current
    if (canvas) {
      canvas.width = canvasSize.width
      canvas.height = canvasSize.height
    }
  }, [canvasSize])

  const resetGame = () => {
    characterRef.current = {
      pos: { x: (WORLD_WIDTH * TILE_SIZE) / 2, y: (WORLD_HEIGHT * TILE_SIZE) / 2 },
      vel: { x: 0, y: 0 },
      size: { x: 28, y: 32 },
      health: 100,
      maxHealth: 100,
      mana: 50,
      maxMana: 50,
      level: 1,
      exp: 0,
      expToNext: 100,
      stats: {
        strength: 10,
        agility: 8,
        vitality: 12,
        intelligence: 6,
      },
      state: "idle",
      direction: 1,
      animFrame: 0,
      animTimer: 0,
      attackTimer: 0,
      weapon: "sword",
      inventory: [
        { id: "sword1", name: "철검", type: "weapon", quantity: 1, description: "튼튼한 철로 만든 검" },
        { id: "potion1", name: "체력 물약", type: "potion", quantity: 3, description: "체력을 회복하는 물약" },
      ],
      gold: 50,
      currentQuest: null,
    }
    enemiesRef.current = []
    setGameState("playing")
    setCurrentNPC(null)
    setShowInventory(false)
  }

  return (
    <div className="w-screen h-screen bg-black overflow-hidden">
      <canvas
        ref={canvasRef}
        width={canvasSize.width}
        height={canvasSize.height}
        className="block"
        tabIndex={0}
        style={{ cursor: "none" }}
      />

      {/* 게임 컨트롤 (우상단) */}
      <div className="absolute top-4 right-4 z-10">
        <Button onClick={resetGame} className="bg-red-800 hover:bg-red-900 text-white font-bold px-4 py-2 text-sm">
          새 게임
        </Button>
      </div>
    </div>
  )
}
