"use client";

import ChatSystem from "@/components/ChatSystem";
import GameInterface from "@/components/GameInterface";
import { Button } from "@/components/ui/button";
import {
  AUTO_SAVE_INTERVAL,
  OnlinePlayer,
  removePlayerFromOnline,
  saveGameData,
  subscribeToOnlinePlayers,
  updatePlayerPosition,
} from "@/lib/gameData";
import { useCallback, useEffect, useRef, useState } from "react";

interface Vector2 {
  x: number;
  y: number;
}

interface Camera {
  x: number;
  y: number;
  targetX: number;
  targetY: number;
}

interface Character {
  pos: Vector2;
  vel: Vector2;
  size: Vector2;
  health: number;
  maxHealth: number;
  mana: number;
  maxMana: number;
  level: number;
  exp: number;
  expToNext: number;
  stats: {
    strength: number;
    agility: number;
    vitality: number;
    intelligence: number;
  };
  state: "idle" | "walking" | "attacking" | "talking";
  direction: number;
  animFrame: number;
  animTimer: number;
  attackTimer: number;
  weapon: "claws";
  inventory: InventoryItem[];
  gold: number;
  invulnerable: number; // 무적 시간 (프레임 단위)
  equipped: EquippedItems;
}

interface InventoryItem {
  id: string;
  name: string;
  type: "weapon" | "armor" | "potion" | "misc";
  quantity: number;
  description: string;
  icon?: string;
  rarity?: "common" | "uncommon" | "rare" | "epic" | "legendary";
  stats?: {
    attack?: number;
    defense?: number;
    health?: number;
    mana?: number;
  };
}

interface EquippedItems {
  weapon?: InventoryItem;
  armor?: InventoryItem;
  accessory?: InventoryItem;
}

interface NPC {
  id: string;
  pos: Vector2;
  size: Vector2;
  name: string;
  type: "villager" | "merchant" | "guard" | "elder";
  dialogue: string[];
  currentDialogue: number;
  animFrame: number;
  animTimer: number;
}

interface Enemy {
  id: number;
  pos: Vector2;
  vel: Vector2;
  size: Vector2;
  health: number;
  maxHealth: number;
  type: "dog" | "bigDog" | "streetCat" | "blackCat" | "puppy";
  state: "idle" | "patrolling" | "chasing" | "attacking" | "hit" | "dying";
  direction: number;
  animFrame: number;
  animTimer: number;
  hitTimer: number;
  deathTimer: number;
  knockback: Vector2;
  attackCooldown: number;
  expReward: number;
  patrolCenter: Vector2;
  patrolRadius: number;
  aggroRange: number;
}

interface WorldTile {
  type:
    | "alley" // 골목길 (기본 지형)
    | "park" // 공원 (인간들이 밥을 주는 곳)
    | "shrine" // 신사 (신비한 고양이들)
    | "factory" // 폐공장 (위험하지만 보상 좋음)
    | "rooftop" // 옥상 (고양이들만 접근 가능)
    | "dumpster" // 쓰레기통 (밥 발견 가능)
    | "house" // 집 (집고양이들 거주)
    | "street"; // 길 (이동 경로)
  walkable: boolean;
  color: string;
}

interface Particle {
  pos: Vector2;
  vel: Vector2;
  life: number;
  maxLife: number;
  color: string;
  size: number;
  type: "spark" | "leaf" | "coin" | "blood";
}

// 게임 클리어 조건: 레벨 10 달성
const GAME_CLEAR_LEVEL = 10;

interface JoseonRPGImprovedProps {
  user?: any;
  isGuest?: boolean;
  guestName?: string;
  initialGameData?: any;
  onGameDataChange?: (data: any) => void;
}

export default function JoseonRPGImproved({
  user,
  isGuest = false,
  guestName = "",
  initialGameData,
  onGameDataChange,
}: JoseonRPGImprovedProps = {}) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const keysRef = useRef<Set<string>>(new Set());
  const lastTimeRef = useRef<number>(0);

  const [gameState, setGameState] = useState<
    | "playing"
    | "dialogue"
    | "inventory"
    | "gameOver"
    | "confirmReset"
    | "gameClear"
  >("playing");
  const [currentNPC, setCurrentNPC] = useState<NPC | null>(null);
  const [showInventory, setShowInventory] = useState(false);
  const [selectedInventoryTab, setSelectedInventoryTab] = useState<
    "all" | "weapon" | "armor" | "potion" | "misc"
  >("all");
  const [hoveredItem, setHoveredItem] = useState<InventoryItem | null>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 1200, height: 520 }); // 상단 인터페이스(80px) 제외
  const [isChatVisible, setIsChatVisible] = useState(true); // 기본적으로 표시
  const [onlinePlayers, setOnlinePlayers] = useState<OnlinePlayer[]>([]);

  // 월드 설정
  const WORLD_WIDTH = 150;
  const WORLD_HEIGHT = 150;
  const TILE_SIZE = 32;

  // 카메라
  const cameraRef = useRef<Camera>({
    x: 0,
    y: 0,
    targetX: 0,
    targetY: 0,
  });

  // 기본 게임 데이터
  const getDefaultGameData = () => ({
    pos: {
      x: (WORLD_WIDTH * TILE_SIZE) / 2,
      y: (WORLD_HEIGHT * TILE_SIZE) / 2,
    },
    vel: { x: 0, y: 0 },
    size: { x: 24, y: 32 },
    health: 100,
    maxHealth: 100,
    mana: 50,
    maxMana: 50,
    level: 1,
    exp: 0,
    expToNext: 100,
    stats: {
      strength: 10,
      agility: 8,
      vitality: 12,
      intelligence: 6,
    },
    state: "idle" as const,
    direction: 1,
    animFrame: 0,
    animTimer: 0,
    attackTimer: 0,
    weapon: "claws" as const,
    inventory: [
      {
        id: "sword1",
        name: "날카로운 발톱",
        type: "weapon" as const,
        quantity: 1,
        description: "예리하게 갈아낸 천연 발톱",
        icon: "🐾",
        rarity: "common" as const,
        stats: { attack: 15 },
      },
      {
        id: "potion1",
        name: "츄르",
        type: "potion" as const,
        quantity: 3,
        description: "맛있는 고양이 간식으로 체력 회복",
        icon: "🍥",
        rarity: "common" as const,
        stats: { health: 50 },
      },
      {
        id: "leather_armor",
        name: "털이불",
        type: "armor" as const,
        quantity: 1,
        description: "따뜻하고 포근한 방어용 털이불",
        icon: "🧣",
        rarity: "common" as const,
        stats: { defense: 8 },
      },
    ],
    gold: 50,
    invulnerable: 0,
    equipped: {
      weapon: {
        id: "sword1",
        name: "날카로운 발톱",
        type: "weapon" as const,
        quantity: 1,
        description: "예리하게 갈아낸 천연 발톱",
        icon: "🐾",
        rarity: "common" as const,
        stats: { attack: 15 },
      },
    },
  });

  // 캐릭터 초기화 (Firebase 데이터 또는 기본 데이터 사용)
  const initializeCharacter = useCallback(() => {
    const defaultData = getDefaultGameData();

    if (initialGameData) {
      // Firebase에서 로드한 데이터가 있으면 사용
      return {
        ...defaultData,
        ...initialGameData,
        // 게임 실행에 필요한 필드들은 기본값 유지
        vel: { x: 0, y: 0 },
        size: { x: 24, y: 32 },
        state: "idle" as const,
        direction: 1,
        animFrame: 0,
        animTimer: 0,
        attackTimer: 0,
        invulnerable: 0,
        pos: initialGameData.position
          ? { x: initialGameData.position.x, y: initialGameData.position.y }
          : defaultData.pos,
      };
    }

    return defaultData;
  }, [initialGameData]);

  // 캐릭터
  const characterRef = useRef<Character>(initializeCharacter());

  // 게임 오브젝트들
  const enemiesRef = useRef<Enemy[]>([]);
  const npcsRef = useRef<NPC[]>([]);
  const particlesRef = useRef<Particle[]>([]);

  // 자동 저장 타이머
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 게임 데이터 변경 시 Firebase에 저장
  const saveToFirebase = useCallback(
    async (character: Character) => {
      if (!user) return;

      const gameData = {
        level: character.level,
        exp: character.exp,
        expToNext: character.expToNext,
        health: character.health,
        maxHealth: character.maxHealth,
        mana: character.mana,
        maxMana: character.maxMana,
        gold: character.gold,
        stats: character.stats,
        inventory: character.inventory,
        equipped: character.equipped,
        position: { x: character.pos.x, y: character.pos.y },
      };

      try {
        await saveGameData(user, gameData);
        if (onGameDataChange) {
          onGameDataChange(gameData);
        }
      } catch (error) {
        console.error("Failed to save game data:", error);
      }
    },
    [user, onGameDataChange]
  );

  // 자동 저장 시작
  useEffect(() => {
    if (!user) return;

    const startAutoSave = () => {
      if (autoSaveTimerRef.current) {
        clearInterval(autoSaveTimerRef.current);
      }

      autoSaveTimerRef.current = setInterval(() => {
        if (gameState === "playing") {
          saveToFirebase(characterRef.current);
        }
      }, AUTO_SAVE_INTERVAL);
    };

    startAutoSave();

    return () => {
      if (autoSaveTimerRef.current) {
        clearInterval(autoSaveTimerRef.current);
      }
    };
  }, [user, gameState, saveToFirebase]);

  // 온라인 플레이어 구독
  useEffect(() => {
    if (!user) return;

    let unsubscribe: (() => void) | null = null;

    try {
      unsubscribe = subscribeToOnlinePlayers(user.uid, (players) => {
        console.log("Online players updated:", players);
        console.log("Current user ID:", user.uid);
        console.log("Players count:", players.length);
        setOnlinePlayers(players);
      });
    } catch (error) {
      console.error("Error subscribing to online players:", error);
      setOnlinePlayers([]);
    }

    return () => {
      if (unsubscribe) {
        try {
          unsubscribe();
        } catch (error) {
          console.error("Error unsubscribing from online players:", error);
        }
      }
    };
  }, [user]);

  // 플레이어 위치 업데이트
  useEffect(() => {
    if (!user || gameState !== "playing") return;

    let updateInterval: NodeJS.Timeout;

    const updatePosition = async () => {
      try {
        const character = characterRef.current;
        if (!character) return;

        const displayName =
          user.displayName || user.email?.split("@")[0] || "무명의 길냥이";

        console.log(
          `🎮 Updating position for ${displayName}: (${character.pos.x}, ${character.pos.y}), level: ${character.level}`
        );
        console.log(`🔐 Current user auth state:`, {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
        });

        const success = await updatePlayerPosition(
          user,
          displayName,
          character.level,
          { x: character.pos.x, y: character.pos.y },
          character.direction,
          character.state,
          character.health,
          character.maxHealth
        );

        if (success) {
          console.log("✅ Position update succeeded!");
        } else {
          console.log("❌ Position update failed!");
        }
      } catch (error) {
        console.error("💥 Error in position update:", error);
      }
    };

    // 초기 위치 업데이트
    updatePosition();

    // 주기적 업데이트 (1초마다)
    updateInterval = setInterval(updatePosition, 1000);

    return () => {
      if (updateInterval) {
        clearInterval(updateInterval);
      }
    };
  }, [user, gameState]);

  // 컴포넌트 언마운트 시 온라인 상태 정리
  useEffect(() => {
    return () => {
      if (user) {
        try {
          removePlayerFromOnline(user);
        } catch (error) {
          console.error("Error removing player from online:", error);
        }
      }
    };
  }, [user]);

  // 월드 맵
  const worldMapRef = useRef<WorldTile[][]>([]);

  // 아이템 사용 함수
  const useItem = useCallback((item: InventoryItem) => {
    const character = characterRef.current;

    switch (item.type) {
      case "potion":
        if (item.id === "potion1") {
          // 체력 물약 사용
          const healAmount = item.stats?.health || 50;
          character.health = Math.min(
            character.maxHealth,
            character.health + healAmount
          );

          // 아이템 수량 감소
          const itemIndex = character.inventory.findIndex(
            (invItem) => invItem.id === item.id
          );
          if (itemIndex !== -1) {
            character.inventory[itemIndex].quantity -= 1;
            if (character.inventory[itemIndex].quantity <= 0) {
              character.inventory.splice(itemIndex, 1);
            }
          }

          // 사운드 효과 (힐링)
          playSound(400, 0.2, "sine", 0.2);
        }
        break;

      case "weapon":
        // 무기 장착
        const oldWeapon = character.equipped.weapon;
        character.equipped.weapon = item;

        // 기존 무기가 있었다면 인벤토리에 추가
        if (oldWeapon && oldWeapon.id !== item.id) {
          const existingItem = character.inventory.find(
            (invItem) => invItem.id === oldWeapon.id
          );
          if (existingItem) {
            existingItem.quantity += 1;
          } else {
            character.inventory.push(oldWeapon);
          }
        }

        // 사운드 효과 (장착)
        playSound(300, 0.15, "square", 0.15);
        break;

      case "armor":
        // 방어구 장착
        const oldArmor = character.equipped.armor;
        character.equipped.armor = item;

        // 기존 방어구가 있었다면 인벤토리에 추가
        if (oldArmor && oldArmor.id !== item.id) {
          const existingItem = character.inventory.find(
            (invItem) => invItem.id === oldArmor.id
          );
          if (existingItem) {
            existingItem.quantity += 1;
          } else {
            character.inventory.push(oldArmor);
          }
        }

        // 사운드 효과 (장착)
        playSound(350, 0.15, "square", 0.15);
        break;
    }
  }, []);

  // 아이템 장착 해제 함수
  const unequipItem = useCallback((slot: keyof EquippedItems) => {
    const character = characterRef.current;
    const equippedItem = character.equipped[slot];

    if (equippedItem) {
      // 인벤토리에 추가
      const existingItem = character.inventory.find(
        (item) => item.id === equippedItem.id
      );
      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        character.inventory.push(equippedItem);
      }

      // 장착 해제
      character.equipped[slot] = undefined;

      // 사운드 효과
      playSound(250, 0.1, "sine", 0.1);
    }
  }, []);

  // 화면 크기 조정
  useEffect(() => {
    const updateCanvasSize = () => {
      setCanvasSize({
        width: window.innerWidth,
        height: window.innerHeight - 80, // 상단 인터페이스(80px) 제외
      });
    };

    updateCanvasSize();
    window.addEventListener("resize", updateCanvasSize);
    return () => window.removeEventListener("resize", updateCanvasSize);
  }, []);

  // 고양이 세계 맵 생성
  const generateWorld = useCallback(() => {
    const world: WorldTile[][] = [];

    // 기본 골목길로 초기화
    for (let y = 0; y < WORLD_HEIGHT; y++) {
      world[y] = [];
      for (let x = 0; x < WORLD_WIDTH; x++) {
        world[y][x] = {
          type: "alley",
          walkable: true,
          color: "#696969", // 회색 골목길
        };
      }
    }

    // 대로 생성 (자동차가 다니는 위험한 길)
    const streetStartX = Math.floor(WORLD_WIDTH * 0.2);
    const streetStartY = Math.floor(WORLD_HEIGHT * 0.1);
    let streetX = streetStartX;
    let streetY = streetStartY;

    for (let i = 0; i < WORLD_HEIGHT * 0.8; i++) {
      // 길 폭 (4-6 타일)
      const streetWidth = 4 + Math.floor(Math.random() * 3);
      for (let w = 0; w < streetWidth; w++) {
        const x = streetX + w - Math.floor(streetWidth / 2);
        const y = streetY;
        if (x >= 0 && x < WORLD_WIDTH && y >= 0 && y < WORLD_HEIGHT) {
          world[y][x] = {
            type: "street",
            walkable: true,
            color: "#2F2F2F", // 아스팔트 색상
          };
        }
      }

      // 길의 자연스러운 흐름
      streetY++;
      if (Math.random() > 0.7) {
        streetX += Math.random() > 0.5 ? 1 : -1;
      }
      streetX = Math.max(5, Math.min(WORLD_WIDTH - 5, streetX));
    }

    // 옥상 지역 생성 (고양이들만 접근 가능한 높은 곳)
    const rooftopCenters = [
      { x: Math.floor(WORLD_WIDTH * 0.8), y: Math.floor(WORLD_HEIGHT * 0.3) },
      { x: Math.floor(WORLD_WIDTH * 0.1), y: Math.floor(WORLD_HEIGHT * 0.7) },
    ];

    rooftopCenters.forEach((center) => {
      const rooftopSize = 12 + Math.floor(Math.random() * 8);
      for (let y = center.y - rooftopSize; y < center.y + rooftopSize; y++) {
        for (let x = center.x - rooftopSize; x < center.x + rooftopSize; x++) {
          if (x >= 0 && x < WORLD_WIDTH && y >= 0 && y < WORLD_HEIGHT) {
            const distance = Math.sqrt(
              (x - center.x) ** 2 + (y - center.y) ** 2
            );
            if (distance < rooftopSize && world[y][x].type === "alley") {
              if (distance < rooftopSize * 0.6) {
                world[y][x] = {
                  type: "rooftop",
                  walkable: true,
                  color: "#8B4513", // 갈색 지붕
                };
              } else if (distance < rooftopSize * 0.8) {
                world[y][x] = {
                  type: "factory",
                  walkable: true,
                  color: "#556B2F", // 어두운 올리브색 폐공장
                };
              }
            }
          }
        }
      }
    });

    // 공원 생성 (인간들이 밥을 주는 곳)
    for (let i = 0; i < 6; i++) {
      const parkX = Math.floor(Math.random() * WORLD_WIDTH);
      const parkY = Math.floor(Math.random() * WORLD_HEIGHT);
      const parkSize = 10 + Math.floor(Math.random() * 15);

      for (let y = parkY - parkSize; y < parkY + parkSize; y++) {
        for (let x = parkX - parkSize; x < parkX + parkSize; x++) {
          if (x >= 0 && x < WORLD_WIDTH && y >= 0 && y < WORLD_HEIGHT) {
            const distance = Math.sqrt((x - parkX) ** 2 + (y - parkY) ** 2);
            if (
              distance < parkSize &&
              world[y][x].type === "alley" &&
              Math.random() > 0.3
            ) {
              world[y][x] = {
                type: "park",
                walkable: true,
                color: "#228B22", // 초록색 공원
              };
            }
          }
        }
      }
    }

    // 쓰레기통 지역 생성 (밥을 찾을 수 있는 곳)
    for (let i = 0; i < 12; i++) {
      const dumpX = Math.floor(Math.random() * WORLD_WIDTH);
      const dumpY = Math.floor(Math.random() * WORLD_HEIGHT);
      const dumpSize = 3 + Math.floor(Math.random() * 5);

      for (let y = dumpY - dumpSize; y < dumpY + dumpSize; y++) {
        for (let x = dumpX - dumpSize; x < dumpX + dumpSize; x++) {
          if (x >= 0 && x < WORLD_WIDTH && y >= 0 && y < WORLD_HEIGHT) {
            const distance = Math.sqrt((x - dumpX) ** 2 + (y - dumpY) ** 2);
            if (
              distance < dumpSize &&
              world[y][x].type === "alley" &&
              Math.random() > 0.5
            ) {
              world[y][x] = {
                type: "dumpster",
                walkable: true,
                color: "#654321", // 갈색 쓰레기통
              };
            }
          }
        }
      }
    }

    // 신사 생성 (신비한 고양이들이 사는 중앙 성지)
    const shrineX = Math.floor(WORLD_WIDTH / 2);
    const shrineY = Math.floor(WORLD_HEIGHT / 2);
    const shrineSize = 10;

    for (let y = shrineY - shrineSize; y < shrineY + shrineSize; y++) {
      for (let x = shrineX - shrineSize; x < shrineX + shrineSize; x++) {
        if (x >= 0 && x < WORLD_WIDTH && y >= 0 && y < WORLD_HEIGHT) {
          const distance = Math.sqrt((x - shrineX) ** 2 + (y - shrineY) ** 2);
          if (distance < shrineSize) {
            world[y][x] = {
              type: "shrine",
              walkable: true,
              color: "#FF6347", // 주황색 신사
            };
          }
        }
      }
    }

    // 집 지역 생성 (집고양이들이 사는 곳)
    for (let i = 0; i < 15; i++) {
      const houseX = Math.floor(Math.random() * WORLD_WIDTH);
      const houseY = Math.floor(Math.random() * WORLD_HEIGHT);
      const houseSize = 4 + Math.floor(Math.random() * 6);

      for (let y = houseY - houseSize; y < houseY + houseSize; y++) {
        for (let x = houseX - houseSize; x < houseX + houseSize; x++) {
          if (x >= 0 && x < WORLD_WIDTH && y >= 0 && y < WORLD_HEIGHT) {
            const distance = Math.sqrt((x - houseX) ** 2 + (y - houseY) ** 2);
            if (distance < houseSize && world[y][x].type === "alley") {
              world[y][x] = {
                type: "house",
                walkable: true,
                color: "#DEB887", // 베이지색 집
              };
            }
          }
        }
      }
    }

    // 좁은 골목길 생성 (신사에서 뻗어나가는)
    const alleys = [
      { dx: 1, dy: 0, length: 25 }, // 동쪽
      { dx: -1, dy: 0, length: 25 }, // 서쪽
      { dx: 0, dy: 1, length: 20 }, // 남쪽
      { dx: 0, dy: -1, length: 20 }, // 북쪽
    ];

    alleys.forEach((alley) => {
      let x = shrineX;
      let y = shrineY;

      for (let i = 0; i < alley.length; i++) {
        x += alley.dx;
        y += alley.dy;

        if (x >= 0 && x < WORLD_WIDTH && y >= 0 && y < WORLD_HEIGHT) {
          if (world[y][x].walkable && world[y][x].type !== "shrine") {
            world[y][x] = {
              type: "alley",
              walkable: true,
              color: "#808080", // 더 밝은 회색 골목길
            };
          }
        }
      }
    });

    worldMapRef.current = world;
  }, []);

  // NPC 생성
  const generateNPCs = useCallback(() => {
    const npcs: NPC[] = [];

    // 마을 장로
    npcs.push({
      id: "elder1",
      pos: {
        x: (WORLD_WIDTH * TILE_SIZE) / 2 - 60,
        y: (WORLD_HEIGHT * TILE_SIZE) / 2 - 40,
      },
      size: { x: 24, y: 32 },
      name: "마을 장로",
      type: "elder",
      dialogue: [
        "어서 오시게, 젊은 무사여.",
        "이 마을에 도적들이 나타나고 있다네.",
        "마을을 지켜주면 보상을 주겠네.",
      ],
      currentDialogue: 0,
      animFrame: 0,
      animTimer: 0,
    });

    // 상인
    npcs.push({
      id: "merchant1",
      pos: {
        x: (WORLD_WIDTH * TILE_SIZE) / 2 + 40,
        y: (WORLD_HEIGHT * TILE_SIZE) / 2 + 20,
      },
      size: { x: 24, y: 32 },
      name: "발톱 장인",
      type: "merchant",
      dialogue: [
        "좋은 발톱이 필요하신가요?",
        "최고 품질의 발톱을 깎아드립니다!",
      ],
      currentDialogue: 0,
      animFrame: 0,
      animTimer: 0,
    });

    // 마을 사람들 - 더 넓게 분산 배치
    for (let i = 0; i < 5; i++) {
      const angle = (i * Math.PI * 2) / 5; // 원형으로 배치
      const distance = 80 + Math.random() * 60; // 80-140 픽셀 거리

      npcs.push({
        id: `villager${i}`,
        pos: {
          x: (WORLD_WIDTH * TILE_SIZE) / 2 + Math.cos(angle) * distance,
          y: (WORLD_HEIGHT * TILE_SIZE) / 2 + Math.sin(angle) * distance,
        },
        size: { x: 24, y: 32 },
        name: `마을 사람 ${i + 1}`,
        type: "villager",
        dialogue: ["안녕하세요!", "좋은 하루입니다.", "조심히 다니세요."],
        currentDialogue: 0,
        animFrame: 0,
        animTimer: 0,
      });
    }

    npcsRef.current = npcs;
  }, []);

  // 적 스폰
  const spawnEnemies = useCallback(() => {
    const character = characterRef.current;
    const enemies = enemiesRef.current;

    if (enemies.length < 15 && Math.random() < 0.008) {
      const spawnDistance = 300 + Math.random() * 200;
      const angle = Math.random() * Math.PI * 2;

      const spawnX = character.pos.x + Math.cos(angle) * spawnDistance;
      const spawnY = character.pos.y + Math.sin(angle) * spawnDistance;

      // 스폰 위치가 걸을 수 있는 곳인지 확인
      const tileX = Math.floor(spawnX / TILE_SIZE);
      const tileY = Math.floor(spawnY / TILE_SIZE);

      if (
        tileX >= 0 &&
        tileX < WORLD_WIDTH &&
        tileY >= 0 &&
        tileY < WORLD_HEIGHT &&
        worldMapRef.current[tileY][tileX].walkable
      ) {
        const enemyTypes: Enemy["type"][] = [
          "dog",
          "bigDog",
          "streetCat",
          "puppy",
        ];
        const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];

        let health, expReward, size, aggroRange;
        switch (type) {
          case "dog":
            health = 4;
            expReward = 20;
            size = { x: 24, y: 32 };
            aggroRange = 100;
            break;
          case "bigDog":
            health = 6;
            expReward = 30;
            size = { x: 32, y: 24 };
            aggroRange = 80;
            break;
          case "streetCat":
            health = 10;
            expReward = 50;
            size = { x: 40, y: 28 };
            aggroRange = 120;
            break;
          case "puppy":
            health = 3;
            expReward = 15;
            size = { x: 20, y: 28 };
            aggroRange = 90;
            break;
          default:
            health = 4;
            expReward = 20;
            size = { x: 24, y: 32 };
            aggroRange = 100;
        }

        enemiesRef.current.push({
          id: Date.now() + Math.random(),
          pos: { x: spawnX, y: spawnY },
          vel: { x: 0, y: 0 },
          size,
          health,
          maxHealth: health,
          type,
          state: "patrolling",
          direction: 1,
          animFrame: 0,
          animTimer: 0,
          hitTimer: 0,
          deathTimer: 0,
          knockback: { x: 0, y: 0 },
          attackCooldown: 0,
          expReward,
          patrolCenter: { x: spawnX, y: spawnY },
          patrolRadius: 80,
          aggroRange,
        });
      }
    }
  }, []);

  // 사운드 효과
  const playSound = useCallback(
    (
      frequency: number,
      duration: number,
      type: "sine" | "square" | "sawtooth" = "sine",
      volume = 0.1
    ) => {
      try {
        const audioContext = new (window.AudioContext ||
          (window as any).webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(
          frequency,
          audioContext.currentTime
        );
        oscillator.type = type;

        gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(
          0.001,
          audioContext.currentTime + duration
        );

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration);
      } catch (e) {
        // 사운드 실패 시 무시
      }
    },
    []
  );

  // 키보드 입력
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.key.toLowerCase());

      if (e.key === "i" || e.key === "I") {
        setShowInventory(!showInventory);
      }

      // 채팅창 토글 (C 키)
      if (e.key === "c" || e.key === "C") {
        setIsChatVisible(!isChatVisible);
      }

      // 인벤토리 탭 변경 (1-5번 키)
      if (showInventory && ["1", "2", "3", "4", "5"].includes(e.key)) {
        const tabs = ["all", "weapon", "armor", "potion", "misc"];
        const tabIndex = parseInt(e.key) - 1;
        if (tabIndex >= 0 && tabIndex < tabs.length) {
          setSelectedInventoryTab(
            tabs[tabIndex] as "all" | "weapon" | "armor" | "potion" | "misc"
          );
        }
      }

      if (e.key === "Escape") {
        setGameState("playing");
        setCurrentNPC(null);
        setShowInventory(false);
        setSelectedInventoryTab("all"); // 인벤토리 닫을 때 탭 리셋
        setIsChatVisible(false); // 채팅창도 닫기
      }

      if (e.key === " " && gameState === "dialogue" && currentNPC) {
        if (currentNPC.currentDialogue < currentNPC.dialogue.length - 1) {
          currentNPC.currentDialogue++;
        } else {
          setGameState("playing");
          setCurrentNPC(null);
        }
        return; // 대화 중 스페이스바를 눌렀을 때 공격이 바로 발동되지 않도록 방지
      }

      // 공격
      if (e.key === " " && gameState === "playing") {
        const character = characterRef.current;
        if (character.attackTimer <= 0) {
          character.state = "attacking";
          character.attackTimer = 30;
          playSound(300, 0.15, "square", 0.15);
        }
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.key.toLowerCase());
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, [showInventory, gameState, currentNPC, playSound, isChatVisible]);

  // 충돌 감지
  const checkCollision = useCallback((a: any, b: any) => {
    return (
      a.pos.x < b.pos.x + b.size.x &&
      a.pos.x + a.size.x > b.pos.x &&
      a.pos.y < b.pos.y + b.size.y &&
      a.pos.y + a.size.y > b.pos.y
    );
  }, []);

  // 개선된 타일 충돌 체크 (부드러운 움직임)
  const canMoveTo = useCallback(
    (x: number, y: number, width: number, height: number) => {
      const margin = 2; // 벽에서 약간 떨어져서 이동

      const leftTile = Math.floor((x + margin) / TILE_SIZE);
      const rightTile = Math.floor((x + width - margin) / TILE_SIZE);
      const topTile = Math.floor((y + margin) / TILE_SIZE);
      const bottomTile = Math.floor((y + height - margin) / TILE_SIZE);

      // 경계 체크
      if (
        leftTile < 0 ||
        rightTile >= WORLD_WIDTH ||
        topTile < 0 ||
        bottomTile >= WORLD_HEIGHT
      ) {
        return false;
      }

      // 모든 코너 타일이 걸을 수 있는지 체크
      return (
        worldMapRef.current[topTile][leftTile].walkable &&
        worldMapRef.current[topTile][rightTile].walkable &&
        worldMapRef.current[bottomTile][leftTile].walkable &&
        worldMapRef.current[bottomTile][rightTile].walkable
      );
    },
    []
  );

  // 파티클 생성
  const createParticles = useCallback(
    (
      pos: Vector2,
      type: "spark" | "leaf" | "coin" | "blood",
      count = 8,
      color = "#ff4444"
    ) => {
      for (let i = 0; i < count; i++) {
        const angle = (Math.PI * 2 * i) / count + (Math.random() - 0.5) * 0.5;
        const speed = 2 + Math.random() * 4;

        particlesRef.current.push({
          pos: { x: pos.x, y: pos.y },
          vel: {
            x: Math.cos(angle) * speed,
            y: Math.sin(angle) * speed,
          },
          life: 40,
          maxLife: 40,
          color,
          size: type === "coin" ? 6 : 4,
          type,
        });
      }
    },
    []
  );

  const respawnCharacter = useCallback(() => {
    const character = characterRef.current;
    const initialPosX = (WORLD_WIDTH * TILE_SIZE) / 2;
    const initialPosY = (WORLD_HEIGHT * TILE_SIZE) / 2;

    // 체력 회복
    character.health = character.maxHealth;
    character.mana = character.maxMana;

    // 위치 초기화 (마을 중앙)
    character.pos = { x: initialPosX, y: initialPosY };
    character.vel = { x: 0, y: 0 };

    // 경험치 감소 (예: 현재 경험치의 20% 손실)
    character.exp = Math.max(0, character.exp - character.expToNext * 0.2); // 다음 레벨업까지 필요한 경험치의 20%를 잃는 것으로 변경
    // 황금 감소 (예: 현재 황금의 10% 손실)
    character.gold = Math.max(0, Math.floor(character.gold * 0.9));

    // 무적 시간 부여 (예: 3초)
    character.invulnerable = 180; // 60 FPS 기준 3초

    // 모든 적과 파티클 제거 (클린 리젠)
    enemiesRef.current = [];
    particlesRef.current = [];

    // 리젠 사운드 효과
    playSound(600, 0.3, "sine", 0.3); // 높은 음의 짧은 소리

    setGameState("playing"); // 게임 상태를 다시 'playing'으로 설정
  }, [playSound]);

  // 게임 업데이트
  const updateGame = useCallback(
    (deltaTime: number) => {
      if (gameState !== "playing") return;

      const character = characterRef.current;
      const keys = keysRef.current;
      const camera = cameraRef.current;

      // 캐릭터 업데이트 섹션 내
      if (character.invulnerable > 0) character.invulnerable--;

      // 캐릭터 이동 (부드러운 움직임)
      character.vel.x *= 0.85;
      character.vel.y *= 0.85;

      const speed = 3.5;
      let moving = false;

      if (keys.has("a") || keys.has("arrowleft")) {
        character.vel.x = -speed;
        character.direction = -1;
        moving = true;
      }
      if (keys.has("d") || keys.has("arrowright")) {
        character.vel.x = speed;
        character.direction = 1;
        moving = true;
      }
      if (keys.has("w") || keys.has("arrowup")) {
        character.vel.y = -speed;
        moving = true;
      }
      if (keys.has("s") || keys.has("arrowdown")) {
        character.vel.y = speed;
        moving = true;
      }

      // 공격 타이머
      if (character.attackTimer > 0) {
        character.attackTimer--;
        // 공격 애니메이션 중에는 상태를 'attacking'으로 유지
        character.state = "attacking";
      } else {
        // 공격 타이머가 끝나면 이동 여부에 따라 상태 변경
        if (moving) {
          character.state = "walking";
        } else {
          character.state = "idle";
        }
      }

      // 이동 처리 (X축)
      const newX = character.pos.x + character.vel.x;
      if (
        canMoveTo(newX, character.pos.y, character.size.x, character.size.y)
      ) {
        character.pos.x = newX;
      } else {
        character.vel.x = 0;
      }

      // 이동 처리 (Y축)
      const newY = character.pos.y + character.vel.y;
      if (
        canMoveTo(character.pos.x, newY, character.size.x, character.size.y)
      ) {
        character.pos.y = newY;
      } else {
        character.vel.y = 0;
      }

      // 카메라 업데이트 (더 부드러운 추적)
      camera.targetX = character.pos.x - canvasSize.width / 2;
      camera.targetY = character.pos.y - canvasSize.height / 2;

      camera.x += (camera.targetX - camera.x) * 0.08;
      camera.y += (camera.targetY - camera.y) * 0.08;

      // NPC 상호작용
      if (keys.has("e")) {
        npcsRef.current.forEach((npc) => {
          const distance = Math.sqrt(
            (character.pos.x - npc.pos.x) ** 2 +
              (character.pos.y - npc.pos.y) ** 2
          );
          if (distance < 40) {
            setCurrentNPC(npc);
            setGameState("dialogue");
            npc.currentDialogue = 0;
          }
        });
      }

      // 적 업데이트
      enemiesRef.current.forEach((enemy, index) => {
        if (enemy.state === "dying") {
          enemy.deathTimer++;
          if (enemy.deathTimer > 60) {
            enemiesRef.current.splice(index, 1);
          }
          return;
        }

        // 넉백 처리
        if (enemy.knockback.x !== 0 || enemy.knockback.y !== 0) {
          const newEnemyX = enemy.pos.x + enemy.knockback.x;
          const newEnemyY = enemy.pos.y + enemy.knockback.y;

          if (canMoveTo(newEnemyX, enemy.pos.y, enemy.size.x, enemy.size.y)) {
            enemy.pos.x = newEnemyX;
          }
          if (canMoveTo(enemy.pos.x, newEnemyY, enemy.size.x, enemy.size.y)) {
            enemy.pos.y = newEnemyY;
          }

          enemy.knockback.x *= 0.9;
          enemy.knockback.y *= 0.9;
          if (Math.abs(enemy.knockback.x) < 0.1) enemy.knockback.x = 0;
          if (Math.abs(enemy.knockback.y) < 0.1) enemy.knockback.y = 0;
        } else {
          const distToPlayer = Math.sqrt(
            (character.pos.x - enemy.pos.x) ** 2 +
              (character.pos.y - enemy.pos.y) ** 2
          );

          if (distToPlayer < enemy.aggroRange && enemy.state !== "chasing") {
            enemy.state = "chasing";
          }

          if (enemy.state === "chasing") {
            const dx = character.pos.x - enemy.pos.x;
            const dy = character.pos.y - enemy.pos.y;
            const dist = Math.sqrt(dx * dx + dy * dy);

            if (dist > 0) {
              let speed = 1.8;
              if (enemy.type === "streetCat") speed = 2.2;
              if (enemy.type === "puppy") speed = 2.5;

              enemy.vel.x = (dx / dist) * speed;
              enemy.vel.y = (dy / dist) * speed;
              enemy.direction = dx > 0 ? 1 : -1;
            }
          } else if (enemy.state === "patrolling") {
            const dx = enemy.patrolCenter.x - enemy.pos.x;
            const dy = enemy.patrolCenter.y - enemy.pos.y;
            const distFromCenter = Math.sqrt(dx * dx + dy * dy);

            if (distFromCenter > enemy.patrolRadius) {
              enemy.vel.x = (dx / distFromCenter) * 0.8;
              enemy.vel.y = (dy / distFromCenter) * 0.8;
            } else {
              enemy.vel.x += (Math.random() - 0.5) * 0.3;
              enemy.vel.y += (Math.random() - 0.5) * 0.3;
            }
          }

          // 적 이동
          const enemyNewX = enemy.pos.x + enemy.vel.x;
          const enemyNewY = enemy.pos.y + enemy.vel.y;

          if (canMoveTo(enemyNewX, enemy.pos.y, enemy.size.x, enemy.size.y)) {
            enemy.pos.x = enemyNewX;
          }
          if (canMoveTo(enemy.pos.x, enemyNewY, enemy.size.x, enemy.size.y)) {
            enemy.pos.y = enemyNewY;
          }
        }

        // 피격 상태 처리
        if (enemy.state === "hit") {
          enemy.hitTimer--;
          if (enemy.hitTimer <= 0) {
            enemy.state = "chasing";
          }
        }

        // 적과 충돌 (데미지) 섹션 내
        if (
          checkCollision(character, enemy) &&
          !["dying"].includes(enemy.state) &&
          character.invulnerable <= 0
        ) {
          let damage = 5;
          if (enemy.type === "streetCat") damage = 12;
          if (enemy.type === "bigDog") damage = 8;

          character.health -= damage;
          playSound(150, 0.2, "square", 0.2);

          character.invulnerable = 180; // 3초 무적 (60FPS 기준)

          if (character.health <= 0) {
            respawnCharacter(); // 게임 오버 대신 리젠 함수 호출
          }
        }

        // 공격 범위 체크
        if (character.state === "attacking" && character.attackTimer > 20) {
          const attackRange = {
            pos: {
              x:
                character.pos.x +
                (character.direction > 0 ? character.size.x : -40),
              y: character.pos.y - 8,
            },
            size: { x: 50, y: character.size.y + 16 },
          };

          if (
            checkCollision(attackRange, enemy) &&
            !["hit", "dying"].includes(enemy.state)
          ) {
            enemy.health -= character.stats.strength + 3;
            enemy.state = "hit";
            enemy.hitTimer = 20;

            // 넉백
            const knockbackForce = 12;
            const dx = enemy.pos.x - character.pos.x;
            const dy = enemy.pos.y - character.pos.y;
            const dist = Math.sqrt(dx * dx + dy * dy);

            if (dist > 0) {
              enemy.knockback.x = (dx / dist) * knockbackForce;
              enemy.knockback.y = (dy / dist) * knockbackForce;
            }

            createParticles(
              {
                x: enemy.pos.x + enemy.size.x / 2,
                y: enemy.pos.y + enemy.size.y / 2,
              },
              "blood",
              8,
              "#8b0000"
            );

            playSound(250, 0.2, "sawtooth", 0.2);

            if (enemy.health <= 0) {
              enemy.state = "dying";
              enemy.deathTimer = 0;
              character.exp += enemy.expReward;
              character.gold += 5 + Math.floor(Math.random() * 10);

              // 적 처치 시 간혹 저장 (성능 고려)
              if (user && Math.random() < 0.3) {
                saveToFirebase(character);
              }

              createParticles(
                {
                  x: enemy.pos.x + enemy.size.x / 2,
                  y: enemy.pos.y + enemy.size.y / 2,
                },
                "coin",
                12,
                "#ffd700"
              );

              playSound(400, 0.3, "sine", 0.25);
            }
          }
        }

        // 애니메이션
        enemy.animTimer++;
        if (enemy.animTimer > 12) {
          enemy.animFrame = (enemy.animFrame + 1) % 4;
          enemy.animTimer = 0;
        }
      });

      // 파티클 업데이트
      particlesRef.current = particlesRef.current.filter((particle) => {
        particle.pos.x += particle.vel.x;
        particle.pos.y += particle.vel.y;
        particle.vel.x *= 0.95;
        particle.vel.y *= 0.95;
        particle.life--;
        return particle.life > 0;
      });

      // NPC 애니메이션
      npcsRef.current.forEach((npc) => {
        npc.animTimer++;
        if (npc.animTimer > 20) {
          npc.animFrame = (npc.animFrame + 1) % 2;
          npc.animTimer = 0;
        }
      });

      // 캐릭터 애니메이션
      character.animTimer++;
      if (character.animTimer > (character.state === "walking" ? 8 : 15)) {
        character.animFrame = (character.animFrame + 1) % 4;
        character.animTimer = 0;
      }

      // 적 스폰
      spawnEnemies();

      // 레벨업 체크
      if (character.exp >= character.expToNext) {
        character.level++;
        character.exp = 0;
        character.expToNext = character.level * 100;
        character.stats.strength += 2;
        character.stats.agility += 1;
        character.stats.vitality += 2;
        character.maxHealth = 80 + character.stats.vitality * 5;
        character.health = character.maxHealth;
        playSound(523, 0.5, "sine", 0.2);

        // 게임 클리어 조건 체크
        if (character.level >= GAME_CLEAR_LEVEL) {
          setGameState("gameClear");
        }

        // 레벨업 시 즉시 저장
        if (user) {
          saveToFirebase(character);
        }
      }
    },
    [
      gameState,
      canvasSize,
      canMoveTo,
      checkCollision,
      createParticles,
      playSound,
      spawnEnemies,
      respawnCharacter,
    ]
  );

  // 개선된 캐릭터 렌더링
  const drawCharacter = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      x: number,
      y: number,
      character: Character
    ) => {
      ctx.save();
      ctx.translate(x + character.size.x / 2, y + character.size.y / 2);
      if (character.direction < 0) ctx.scale(-1, 1);

      // 그림자
      ctx.fillStyle = "rgba(0, 0, 0, 0.3)";
      ctx.beginPath();
      ctx.ellipse(
        0,
        character.size.y / 2 - 2,
        character.size.x / 2,
        4,
        0,
        0,
        Math.PI * 2
      );
      ctx.fill();

      // 고양이 몸통 (하체)
      ctx.fillStyle = "#FF8C00"; // 주황색 고양이
      if (
        character.invulnerable > 0 &&
        Math.floor(character.invulnerable / 10) % 2
      ) {
        ctx.fillStyle = "rgba(255, 140, 0, 0.5)"; // 무적 시 반투명하게 깜빡임
      }
      ctx.fillRect(
        -character.size.x / 2,
        character.size.y / 2 - 16,
        character.size.x,
        16
      );

      // 고양이 몸통 (상체)
      ctx.fillRect(
        -character.size.x / 2,
        -character.size.y / 2 + 8,
        character.size.x,
        16
      );

      // 고양이 무늬 (줄무늬)
      ctx.fillStyle = "#FF4500";
      for (let i = 0; i < 3; i++) {
        ctx.fillRect(
          -character.size.x / 2 + 2,
          -character.size.y / 2 + 10 + i * 6,
          character.size.x - 4,
          2
        );
      }

      // 고양이 얼굴 (머리)
      ctx.fillStyle = "#FF8C00";
      ctx.fillRect(-10, -character.size.y / 2 + 2, 20, 14);

      // 고양이 귀
      ctx.fillStyle = "#FF8C00";
      ctx.fillRect(-8, -character.size.y / 2 - 4, 6, 8);
      ctx.fillRect(2, -character.size.y / 2 - 4, 6, 8);

      // 귀 안쪽 (핑크)
      ctx.fillStyle = "#FFB6C1";
      ctx.fillRect(-6, -character.size.y / 2 - 2, 2, 4);
      ctx.fillRect(4, -character.size.y / 2 - 2, 2, 4);

      // 고양이 눈 (큰 눈)
      ctx.fillStyle = "#32CD32"; // 녹색 고양이 눈
      ctx.fillRect(-6, -character.size.y / 2 + 6, 4, 3);
      ctx.fillRect(2, -character.size.y / 2 + 6, 4, 3);

      // 눈동자
      ctx.fillStyle = "#000000";
      ctx.fillRect(-5, -character.size.y / 2 + 7, 1, 2);
      ctx.fillRect(4, -character.size.y / 2 + 7, 1, 2);

      // 고양이 코 (삼각형 모양)
      ctx.fillStyle = "#FFB6C1";
      ctx.fillRect(-1, -character.size.y / 2 + 10, 2, 2);

      // 고양이 수염
      ctx.fillStyle = "#000000";
      ctx.fillRect(-12, -character.size.y / 2 + 8, 4, 1);
      ctx.fillRect(-12, -character.size.y / 2 + 11, 4, 1);
      ctx.fillRect(8, -character.size.y / 2 + 8, 4, 1);
      ctx.fillRect(8, -character.size.y / 2 + 11, 4, 1);

      // 고양이 입 (W 모양)
      ctx.fillStyle = "#000000";
      ctx.fillRect(-2, -character.size.y / 2 + 12, 1, 1);
      ctx.fillRect(0, -character.size.y / 2 + 13, 1, 1);
      ctx.fillRect(2, -character.size.y / 2 + 12, 1, 1);

      // 발톱 공격 이펙트
      if (character.state === "attacking") {
        const attackAnimFrame = character.attackTimer % 30;
        const attackProgress = attackAnimFrame / 30;

        // 발톱 휘두르기 궤적
        ctx.strokeStyle = "#FFFFFF";
        ctx.lineWidth = 4;
        ctx.lineCap = "round";
        ctx.shadowColor = "#FFFFFF";
        ctx.shadowBlur = 8;

        // 3개의 발톱 자국 (할퀴기) - 방향에 따라 변경
        for (let i = 0; i < 3; i++) {
          const clawOffset = i * 4 - 4; // -4, 0, 4

          // 방향에 따른 발톱 공격 위치 계산
          const direction = character.direction > 0 ? 1 : -1;
          const startX =
            (character.size.x / 2 - 8) * direction + clawOffset * direction;
          const startY = -character.size.y / 2 + 4 + Math.abs(clawOffset);
          const endX =
            (character.size.x / 2 + 12) * direction + clawOffset * direction;
          const endY = character.size.y / 2 - 8 + Math.abs(clawOffset);

          // 애니메이션에 따른 발톱 자국 길이
          const animatedEndX = startX + (endX - startX) * attackProgress;
          const animatedEndY = startY + (endY - startY) * attackProgress;

          ctx.beginPath();
          ctx.moveTo(startX, startY);
          ctx.lineTo(animatedEndX, animatedEndY);
          ctx.stroke();
        }

        // 공격 이펙트 파티클 - 방향에 따라 변경
        if (attackAnimFrame < 15) {
          ctx.fillStyle = `rgba(255, 255, 255, ${1 - attackProgress})`;
          for (let i = 0; i < 5; i++) {
            const direction = character.direction > 0 ? 1 : -1;
            const sparkX =
              (character.size.x / 2) * direction +
              (Math.random() * 20 - 10) * direction;
            const sparkY = -character.size.y / 2 + Math.random() * 20;
            ctx.fillRect(sparkX, sparkY, 2, 2);
          }
        }

        ctx.shadowBlur = 0; // 그림자 초기화
      }

      // 고양이 꼬리 (뒤쪽에 그려짐)
      ctx.fillStyle = "#FF8C00";
      const tailX =
        character.direction > 0
          ? -character.size.x / 2 - 8
          : character.size.x / 2 + 2;
      const tailSway = Math.sin(Date.now() / 200) * 3; // 꼬리 흔들기
      ctx.fillRect(tailX, -character.size.y / 2 + tailSway, 6, 20);

      // 꼬리 끝
      ctx.fillStyle = "#FFFFFF";
      ctx.fillRect(tailX + 1, -character.size.y / 2 + tailSway + 16, 4, 4);

      // 고양이 다리 애니메이션 (4개 발)
      ctx.fillStyle = "#FF8C00";
      if (
        character.state === "walking" ||
        (character.state === "attacking" &&
          (keysRef.current.has("w") ||
            keysRef.current.has("a") ||
            keysRef.current.has("s") ||
            keysRef.current.has("d")))
      ) {
        // 걸을 때 다리 애니메이션
        const legOffset = Math.sin(character.animFrame * 0.5) * 2;
        // 앞발
        ctx.fillRect(-8, character.size.y / 2 - 6, 4, 6 - legOffset);
        ctx.fillRect(-2, character.size.y / 2 - 6, 4, 6 + legOffset);
        // 뒷발
        ctx.fillRect(-6, character.size.y / 2 - 4, 3, 4 + legOffset);
        ctx.fillRect(3, character.size.y / 2 - 4, 3, 4 - legOffset);
      } else {
        // 정지 상태
        // 앞발
        ctx.fillRect(-8, character.size.y / 2 - 6, 4, 6);
        ctx.fillRect(-2, character.size.y / 2 - 6, 4, 6);
        // 뒷발
        ctx.fillRect(-6, character.size.y / 2 - 4, 3, 4);
        ctx.fillRect(3, character.size.y / 2 - 4, 3, 4);
      }

      // 발가락 (발톱)
      ctx.fillStyle = "#FFFFFF";
      ctx.fillRect(-8, character.size.y / 2, 1, 1);
      ctx.fillRect(-6, character.size.y / 2, 1, 1);
      ctx.fillRect(-2, character.size.y / 2, 1, 1);
      ctx.fillRect(0, character.size.y / 2, 1, 1);

      ctx.restore();
    },
    []
  );

  // 다른 플레이어 렌더링
  const drawOtherPlayer = useCallback(
    (
      ctx: CanvasRenderingContext2D,
      x: number,
      y: number,
      player: OnlinePlayer
    ) => {
      ctx.save();
      ctx.translate(x + 16, y + 16); // 플레이어 크기 16x16 기준

      // 방향에 따른 스케일
      if (player.direction < 0) ctx.scale(-1, 1);

      // 그림자
      ctx.fillStyle = "rgba(0, 0, 0, 0.3)";
      ctx.beginPath();
      ctx.ellipse(0, 14, 12, 3, 0, 0, Math.PI * 2);
      ctx.fill();

      // 한복 하의 (치마/바지)
      ctx.fillStyle = "#8b4513";
      ctx.fillRect(-8, 0, 16, 16);

      // 한복 상의 - 다른 플레이어는 다른 색상으로 구분
      const playerColors = [
        "#dc143c",
        "#4169e1",
        "#32cd32",
        "#ff8c00",
        "#8a2be2",
      ];
      const colorIndex = player.userId.charCodeAt(0) % playerColors.length;
      ctx.fillStyle = playerColors[colorIndex];
      ctx.fillRect(-8, -16 + 2, 16, 12);

      // 갓 (전통 모자)
      ctx.fillStyle = "#2f1b14";
      ctx.fillRect(-10, -16, 20, 6);
      ctx.fillRect(-2, -16 + 6, 4, 4);

      // 얼굴
      ctx.fillStyle = "#fdbcb4";
      ctx.fillRect(-6, -16 + 6, 12, 8);

      // 눈
      ctx.fillStyle = "#000000";
      ctx.fillRect(-4, -16 + 8, 2, 2);
      ctx.fillRect(2, -16 + 8, 2, 2);

      // 입
      ctx.fillStyle = "#ff6b6b";
      ctx.fillRect(-1, -16 + 10, 2, 1);

      // 체력바 표시
      if (player.health < player.maxHealth) {
        const barWidth = 20;
        const barHeight = 3;
        const healthPercent = player.health / player.maxHealth;

        // 체력바 배경
        ctx.fillStyle = "#333333";
        ctx.fillRect(-barWidth / 2, -25, barWidth, barHeight);

        // 체력바
        ctx.fillStyle =
          healthPercent > 0.5
            ? "#00ff00"
            : healthPercent > 0.2
            ? "#ffff00"
            : "#ff0000";
        ctx.fillRect(-barWidth / 2, -25, barWidth * healthPercent, barHeight);
      }

      ctx.restore();
    },
    []
  );

  // 완전히 수정된 적 렌더링 (버그 수정)
  const drawEnemy = useCallback(
    (ctx: CanvasRenderingContext2D, x: number, y: number, enemy: Enemy) => {
      // 각 적마다 완전히 독립적인 렌더링
      ctx.save();

      // 그림자 먼저 그리기 (독립적으로)
      ctx.fillStyle = "rgba(0, 0, 0, 0.3)";
      ctx.beginPath();
      ctx.ellipse(
        x + enemy.size.x / 2,
        y + enemy.size.y - 2,
        enemy.size.x / 3,
        3,
        0,
        0,
        Math.PI * 2
      );
      ctx.fill();

      // 적 본체 렌더링
      ctx.translate(x + enemy.size.x / 2, y + enemy.size.y / 2);
      if (enemy.direction < 0) ctx.scale(-1, 1);

      let enemyColor = "#8b0000";
      if (enemy.state === "hit") enemyColor = "#ffffff";
      if (enemy.state === "dying") {
        const alpha = 1 - enemy.deathTimer / 60;
        enemyColor = `rgba(139, 0, 0, ${alpha})`;
      }

      switch (enemy.type) {
        case "dog":
          // 개 몸
          ctx.fillStyle = enemyColor;
          ctx.fillRect(
            -enemy.size.x / 2,
            -enemy.size.y / 2,
            enemy.size.x,
            enemy.size.y
          );
          // 개 귀
          ctx.fillStyle = "#8B4513";
          ctx.fillRect(-enemy.size.x / 2, -enemy.size.y / 2, enemy.size.x, 8);
          // 개 얼굴
          ctx.fillStyle = "#fdbcb4";
          ctx.fillRect(-6, -enemy.size.y / 2 + 4, 12, 8);
          // 개 눈
          ctx.fillStyle = "#000000";
          ctx.fillRect(1, -enemy.size.y / 2 + 6, 2, 2);
          break;

        case "bigDog":
          // 멧돼지 몸
          ctx.fillStyle = "#8b4513";
          ctx.fillRect(
            -enemy.size.x / 2,
            -enemy.size.y / 2,
            enemy.size.x,
            enemy.size.y
          );
          // 멧돼지 주둥이
          ctx.fillStyle = "#654321";
          ctx.fillRect(enemy.size.x / 2 - 8, -2, 8, 6);
          // 엄니
          ctx.fillStyle = "#ffffff";
          ctx.fillRect(enemy.size.x / 2 - 6, 0, 4, 2);
          ctx.fillRect(enemy.size.x / 2 - 6, 2, 4, 2);
          // 눈
          ctx.fillStyle = "#000000";
          ctx.fillRect(2, -enemy.size.y / 2 + 4, 2, 2);
          break;

        case "streetCat":
          // 호랑이 몸
          ctx.fillStyle = "#ff8c00";
          ctx.fillRect(
            -enemy.size.x / 2,
            -enemy.size.y / 2,
            enemy.size.x,
            enemy.size.y
          );
          // 호랑이 줄무늬
          ctx.fillStyle = "#000000";
          for (let i = 0; i < 4; i++) {
            ctx.fillRect(
              -enemy.size.x / 2 + i * 8,
              -enemy.size.y / 2,
              3,
              enemy.size.y
            );
          }
          // 얼굴
          ctx.fillStyle = "#ffa500";
          ctx.fillRect(-8, -enemy.size.y / 2 + 2, 16, 10);
          // 눈
          ctx.fillStyle = "#ffff00";
          ctx.fillRect(2, -enemy.size.y / 2 + 4, 3, 3);
          ctx.fillRect(-5, -enemy.size.y / 2 + 4, 3, 3);
          break;

        case "puppy":
          // 고블린 몸
          ctx.fillStyle = "#228b22";
          ctx.fillRect(
            -enemy.size.x / 2,
            -enemy.size.y / 2,
            enemy.size.x,
            enemy.size.y
          );
          // 고블린 얼굴
          ctx.fillStyle = "#32cd32";
          ctx.fillRect(-6, -enemy.size.y / 2 + 2, 12, 10);
          // 뾰족한 귀
          ctx.fillStyle = "#228b22";
          ctx.fillRect(-8, -enemy.size.y / 2 + 2, 4, 6);
          ctx.fillRect(4, -enemy.size.y / 2 + 2, 4, 6);
          // 눈
          ctx.fillStyle = "#ff0000";
          ctx.fillRect(1, -enemy.size.y / 2 + 5, 2, 2);
          ctx.fillRect(-3, -enemy.size.y / 2 + 5, 2, 2);
          break;
      }

      ctx.restore();
    },
    []
  );

  // 개선된 NPC 렌더링
  const drawNPC = useCallback(
    (ctx: CanvasRenderingContext2D, x: number, y: number, npc: NPC) => {
      ctx.save();
      ctx.translate(x + npc.size.x / 2, y + npc.size.y / 2);

      // 개선된 그림자 (더 작고 선명하게)
      ctx.fillStyle = "rgba(0, 0, 0, 0.4)";
      ctx.beginPath();
      ctx.ellipse(0, npc.size.y / 2 - 1, npc.size.x / 3, 3, 0, 0, Math.PI * 2);
      ctx.fill();

      switch (npc.type) {
        case "elder":
          // 장로 한복 (고급)
          ctx.fillStyle = "#4169e1";
          ctx.fillRect(
            -npc.size.x / 2,
            -npc.size.y / 2,
            npc.size.x,
            npc.size.y
          );
          // 얼굴
          ctx.fillStyle = "#fdbcb4";
          ctx.fillRect(-8, -npc.size.y / 2 + 4, 16, 12);
          // 수염
          ctx.fillStyle = "#ffffff";
          ctx.fillRect(-6, -npc.size.y / 2 + 12, 12, 6);
          // 갓 (고급)
          ctx.fillStyle = "#2f1b14";
          ctx.fillRect(
            -npc.size.x / 2 - 2,
            -npc.size.y / 2 - 4,
            npc.size.x + 4,
            8
          );
          break;

        case "merchant":
          // 상인 복장
          ctx.fillStyle = "#daa520";
          ctx.fillRect(
            -npc.size.x / 2,
            -npc.size.y / 2,
            npc.size.x,
            npc.size.y
          );
          // 얼굴
          ctx.fillStyle = "#fdbcb4";
          ctx.fillRect(-8, -npc.size.y / 2 + 4, 16, 12);
          // 상인 모자
          ctx.fillStyle = "#8b4513";
          ctx.fillRect(-npc.size.x / 2, -npc.size.y / 2 - 2, npc.size.x, 6);
          break;

        case "villager":
          // 일반 마을 사람
          const colors = ["#8fbc8f", "#dda0dd", "#f0e68c", "#ffa07a"];
          ctx.fillStyle =
            colors[Number.parseInt(npc.id.slice(-1)) % colors.length];
          ctx.fillRect(
            -npc.size.x / 2,
            -npc.size.y / 2,
            npc.size.x,
            npc.size.y
          );
          // 얼굴
          ctx.fillStyle = "#fdbcb4";
          ctx.fillRect(-6, -npc.size.y / 2 + 4, 12, 10);
          break;
      }

      // 눈
      ctx.fillStyle = "#000000";
      ctx.fillRect(2, -npc.size.y / 2 + 8, 2, 2);
      ctx.fillRect(-4, -npc.size.y / 2 + 8, 2, 2);

      ctx.restore();
    },
    []
  );

  // 렌더링
  const render = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    const character = characterRef.current;
    const camera = cameraRef.current;

    // 화면 클리어
    ctx.fillStyle = "#87ceeb";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 월드 렌더링
    const startTileX = Math.max(0, Math.floor(camera.x / TILE_SIZE));
    const endTileX = Math.min(
      WORLD_WIDTH,
      Math.ceil((camera.x + canvas.width) / TILE_SIZE)
    );
    const startTileY = Math.max(0, Math.floor(camera.y / TILE_SIZE));
    const endTileY = Math.min(
      WORLD_HEIGHT,
      Math.ceil((camera.y + canvas.height) / TILE_SIZE)
    );

    for (let y = startTileY; y < endTileY; y++) {
      for (let x = startTileX; x < endTileX; x++) {
        const tile = worldMapRef.current[y][x];
        const screenX = x * TILE_SIZE - camera.x;
        const screenY = y * TILE_SIZE - camera.y;

        ctx.fillStyle = tile.color;
        ctx.fillRect(screenX, screenY, TILE_SIZE, TILE_SIZE);

        // 타일 디테일
        switch (tile.type) {
          case "forest":
            // 나무
            ctx.fillStyle = "#1a3d1a";
            ctx.fillRect(screenX + 8, screenY + 4, 16, 24);
            ctx.fillStyle = "#2d5a2d";
            ctx.beginPath();
            ctx.arc(screenX + 16, screenY + 8, 12, 0, Math.PI * 2);
            ctx.fill();
            break;
          case "village":
            // 집 모양
            ctx.fillStyle = "#8b4513";
            ctx.fillRect(screenX + 4, screenY + 8, 24, 20);
            ctx.fillStyle = "#dc143c";
            ctx.beginPath();
            ctx.moveTo(screenX + 4, screenY + 8);
            ctx.lineTo(screenX + 16, screenY + 2);
            ctx.lineTo(screenX + 28, screenY + 8);
            ctx.fill();
            break;
          case "water":
            // 물결 효과
            ctx.fillStyle = "#4682b4";
            ctx.fillRect(screenX + 4, screenY + 8, 24, 4);
            ctx.fillRect(screenX + 8, screenY + 16, 16, 4);
            break;
          case "mountain":
            // 산 모양
            ctx.fillStyle = "#696969";
            ctx.beginPath();
            ctx.moveTo(screenX, screenY + TILE_SIZE);
            ctx.lineTo(screenX + 16, screenY + 4);
            ctx.lineTo(screenX + TILE_SIZE, screenY + TILE_SIZE);
            ctx.fill();
            break;
        }
      }
    }

    // NPC 렌더링
    npcsRef.current.forEach((npc) => {
      const screenX = npc.pos.x - camera.x;
      const screenY = npc.pos.y - camera.y;

      if (
        screenX > -50 &&
        screenX < canvas.width + 50 &&
        screenY > -50 &&
        screenY < canvas.height + 50
      ) {
        drawNPC(ctx, screenX, screenY, npc);

        // 이름 표시
        ctx.fillStyle = "#ffffff";
        ctx.font = "12px serif";
        ctx.textAlign = "center";
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = 3;
        ctx.strokeText(npc.name, screenX + npc.size.x / 2, screenY - 8);
        ctx.fillText(npc.name, screenX + npc.size.x / 2, screenY - 8);

        // 상호작용 표시
        const distToPlayer = Math.sqrt(
          (character.pos.x - npc.pos.x) ** 2 +
            (character.pos.y - npc.pos.y) ** 2
        );
        if (distToPlayer < 40) {
          ctx.fillStyle = "#ffd700";
          ctx.font = "10px serif";
          ctx.strokeText("E키로 대화", screenX + npc.size.x / 2, screenY - 20);
          ctx.fillText("E키로 대화", screenX + npc.size.x / 2, screenY - 20);
        }
      }
    });

    // 적 렌더링 (개선된 버전)
    enemiesRef.current.forEach((enemy) => {
      const screenX = enemy.pos.x - camera.x;
      const screenY = enemy.pos.y - camera.y;

      if (
        screenX > -50 &&
        screenX < canvas.width + 50 &&
        screenY > -50 &&
        screenY < canvas.height + 50
      ) {
        // 적 그리기
        drawEnemy(ctx, screenX, screenY, enemy);

        // 명확한 HP바 렌더링
        if (enemy.state !== "dying") {
          const hpBarWidth = enemy.size.x + 4;
          const hpBarHeight = 8;
          const hpBarX = screenX - 2;
          const hpBarY = screenY - 15;

          // HP바 배경 (검은색)
          ctx.fillStyle = "rgba(0, 0, 0, 0.8)";
          ctx.fillRect(hpBarX, hpBarY, hpBarWidth, hpBarHeight);

          // HP바 테두리 (흰색)
          ctx.strokeStyle = "#ffffff";
          ctx.lineWidth = 1;
          ctx.strokeRect(hpBarX, hpBarY, hpBarWidth, hpBarHeight);

          // 현재 HP (빨간색)
          const currentHpWidth =
            (enemy.health / enemy.maxHealth) * (hpBarWidth - 2);
          ctx.fillStyle = "#ff0000";
          ctx.fillRect(hpBarX + 1, hpBarY + 1, currentHpWidth, hpBarHeight - 2);

          // HP 텍스트
          ctx.fillStyle = "#ffffff";
          ctx.font = "10px Arial";
          ctx.textAlign = "center";
          ctx.strokeStyle = "#000000";
          ctx.lineWidth = 2;
          const hpText = `${enemy.health}/${enemy.maxHealth}`;
          ctx.strokeText(hpText, screenX + enemy.size.x / 2, hpBarY + 6);
          ctx.fillText(hpText, screenX + enemy.size.x / 2, hpBarY + 6);

          // 적 타입 표시
          ctx.fillStyle = "#ffff00";
          ctx.font = "8px Arial";
          let typeName = "";
          switch (enemy.type) {
            case "dog":
              typeName = "개";
              break;
            case "bigDog":
              typeName = "큰개";
              break;
            case "streetCat":
              typeName = "길냥이";
              break;
            case "puppy":
              typeName = "강아지";
              break;
          }
          ctx.strokeText(
            typeName,
            screenX + enemy.size.x / 2,
            screenY + enemy.size.y + 12
          );
          ctx.fillText(
            typeName,
            screenX + enemy.size.x / 2,
            screenY + enemy.size.y + 12
          );
        }
      }
    });

    // 캐릭터 렌더링
    const screenX = character.pos.x - camera.x;
    const screenY = character.pos.y - camera.y;
    drawCharacter(ctx, screenX, screenY, character);

    // 캐릭터 이름 표시
    if (user) {
      const displayName =
        user.displayName || user.email?.split("@")[0] || "무명의 길냥이";
      ctx.save();
      ctx.font = "bold 12px Arial";
      ctx.fillStyle = "#ffffff";
      ctx.strokeStyle = "#000000";
      ctx.lineWidth = 2;
      ctx.textAlign = "center";

      // 이름 위치 (캐릭터 위쪽)
      const nameX = screenX + character.size.x / 2;
      const nameY = screenY - 8;

      // 텍스트 테두리
      ctx.strokeText(displayName, nameX, nameY);
      // 텍스트 채우기
      ctx.fillText(displayName, nameX, nameY);

      // 레벨도 함께 표시
      const levelText = `Lv.${character.level}`;
      ctx.font = "10px Arial";
      ctx.fillStyle = "#ffff00";
      ctx.strokeText(levelText, nameX, nameY - 14);
      ctx.fillText(levelText, nameX, nameY - 14);

      ctx.restore();
    }

    // 다른 플레이어들 렌더링
    console.log("Rendering online players:", onlinePlayers.length);
    onlinePlayers.forEach((player, index) => {
      const playerScreenX = player.position.x - camera.x;
      const playerScreenY = player.position.y - camera.y;
      console.log(
        `Player ${index}: ${player.displayName} at (${player.position.x}, ${player.position.y}) -> screen (${playerScreenX}, ${playerScreenY})`
      );

      // 화면에 보이는 플레이어만 렌더링
      if (
        playerScreenX > -50 &&
        playerScreenX < canvas.width + 50 &&
        playerScreenY > -50 &&
        playerScreenY < canvas.height + 50
      ) {
        console.log(`Drawing player: ${player.displayName}`);
        drawOtherPlayer(ctx, playerScreenX, playerScreenY, player);

        // 다른 플레이어 이름 표시
        ctx.save();
        ctx.font = "bold 11px Arial";
        ctx.fillStyle = "#ffffff";
        ctx.strokeStyle = "#000000";
        ctx.lineWidth = 2;
        ctx.textAlign = "center";

        const nameX = playerScreenX + 16;
        const nameY = playerScreenY - 8;

        // 텍스트 테두리
        ctx.strokeText(player.displayName, nameX, nameY);
        // 텍스트 채우기
        ctx.fillText(player.displayName, nameX, nameY);

        // 레벨 표시
        const levelText = `Lv.${player.level}`;
        ctx.font = "9px Arial";
        ctx.fillStyle = "#ffff00";
        ctx.strokeText(levelText, nameX, nameY - 12);
        ctx.fillText(levelText, nameX, nameY - 12);

        ctx.restore();
      }
    });

    // 파티클 렌더링
    particlesRef.current.forEach((particle) => {
      const alpha = particle.life / particle.maxLife;
      const particleScreenX = particle.pos.x - camera.x;
      const particleScreenY = particle.pos.y - camera.y;

      ctx.save();
      ctx.globalAlpha = alpha;

      switch (particle.type) {
        case "coin":
          ctx.fillStyle = particle.color;
          ctx.beginPath();
          ctx.arc(
            particleScreenX,
            particleScreenY,
            particle.size,
            0,
            Math.PI * 2
          );
          ctx.fill();
          break;
        case "blood":
          ctx.fillStyle = particle.color;
          ctx.fillRect(
            particleScreenX - particle.size / 2,
            particleScreenY - particle.size / 2,
            particle.size,
            particle.size
          );
          break;
        default:
          ctx.fillStyle = particle.color;
          ctx.fillRect(
            particleScreenX - particle.size / 2,
            particleScreenY - particle.size / 2,
            particle.size,
            particle.size
          );
          break;
      }

      ctx.restore();
    });

    // UI 렌더링 - HUD는 이제 React 컴포넌트로 대체됨
    // 미니맵만 남김

    // 기존 HUD 코드는 React 컴포넌트로 대체됨

    // 미니맵은 이제 GameInterface 컴포넌트로 이동됨

    // 조작법
    ctx.fillStyle = "rgba(244, 228, 188, 0.9)";
    ctx.fillRect(10, canvas.height - 40, 400, 30);
    ctx.strokeRect(10, canvas.height - 40, 400, 30);
    ctx.fillStyle = "#2f1b14";
    ctx.font = "12px serif";
    ctx.fillText(
      "WASD / 방향키: 이동 | 스페이스: 할퀴기 | E: 상호작용 | I: 냥용품",
      15,
      canvas.height - 20
    );

    // 대화창
    if (gameState === "dialogue" && currentNPC) {
      const dialogueHeight = 120;
      const dialogueY = canvas.height - dialogueHeight - 50;

      ctx.fillStyle = "rgba(244, 228, 188, 0.98)";
      ctx.fillRect(20, dialogueY, canvas.width - 40, dialogueHeight);
      ctx.strokeStyle = "#2f1b14";
      ctx.lineWidth = 4;
      ctx.strokeRect(20, dialogueY, canvas.width - 40, dialogueHeight);

      ctx.fillStyle = "#2f1b14";
      ctx.font = "bold 18px serif";
      ctx.fillText(currentNPC.name, 35, dialogueY + 30);

      ctx.font = "16px serif";
      const dialogue = currentNPC.dialogue[currentNPC.currentDialogue];
      ctx.fillText(dialogue, 35, dialogueY + 60);

      ctx.font = "12px serif";
      ctx.fillText("스페이스바로 계속...", 35, dialogueY + 100);
    }

    // 인벤토리 - 중앙 위치
    if (showInventory) {
      const invWidth = 700;
      const invHeight = 400;
      const invX = (canvas.width - invWidth) / 2;
      const invY = (canvas.height - invHeight) / 2;

      // 인벤토리 배경
      ctx.fillStyle = "rgba(20, 15, 10, 0.95)";
      ctx.fillRect(invX, invY, invWidth, invHeight);
      ctx.strokeStyle = "#8B4513";
      ctx.lineWidth = 4;
      ctx.strokeRect(invX, invY, invWidth, invHeight);

      // 장식적인 테두리
      ctx.strokeStyle = "#DAA520";
      ctx.lineWidth = 2;
      ctx.strokeRect(invX + 10, invY + 10, invWidth - 20, invHeight - 20);

      // 제목 배경
      ctx.fillStyle = "rgba(139, 69, 19, 0.8)";
      ctx.fillRect(invX + 15, invY + 15, invWidth - 30, 60);
      ctx.strokeStyle = "#DAA520";
      ctx.lineWidth = 2;
      ctx.strokeRect(invX + 15, invY + 15, invWidth - 30, 60);

      ctx.fillStyle = "#FFD700";
      ctx.font = "bold 28px serif";
      ctx.textAlign = "center";
      ctx.fillText("🧳 냥용품", invX + invWidth / 2, invY + 50);

      // 탭 버튼들
      const tabs = [
        { id: "all", name: "전체", icon: "📦" },
        { id: "weapon", name: "발톱", icon: "🐾" },
        { id: "armor", name: "털용품", icon: "🧣" },
        { id: "potion", name: "간식", icon: "🍥" },
        { id: "misc", name: "기타", icon: "🎁" },
      ];

      const tabWidth = 100;
      const tabHeight = 40;
      const tabStartX = invX + 50;

      tabs.forEach((tab, index) => {
        const tabX = tabStartX + index * (tabWidth + 10);
        const tabY = invY + 85;
        const isSelected = selectedInventoryTab === tab.id;

        // 탭 배경
        ctx.fillStyle = isSelected
          ? "rgba(255, 215, 0, 0.3)"
          : "rgba(139, 69, 19, 0.5)";
        ctx.fillRect(tabX, tabY, tabWidth, tabHeight);

        // 탭 테두리
        ctx.strokeStyle = isSelected ? "#FFD700" : "#8B4513";
        ctx.lineWidth = 2;
        ctx.strokeRect(tabX, tabY, tabWidth, tabHeight);

        // 탭 텍스트
        ctx.fillStyle = isSelected ? "#FFD700" : "#D2B48C";
        ctx.font = "14px serif";
        ctx.textAlign = "center";
        ctx.fillText(tab.icon, tabX + tabWidth / 2, tabY + 15);
        ctx.font = "10px serif";
        ctx.fillText(tab.name, tabX + tabWidth / 2, tabY + 30);
      });

      // 장비 슬롯 영역
      const equipX = invX + 50;
      const equipY = invY + 140;
      const slotSize = 60;
      const slotGap = 10;

      ctx.fillStyle = "rgba(139, 69, 19, 0.7)";
      ctx.fillRect(equipX - 10, equipY - 10, 200, 80);
      ctx.strokeStyle = "#8B4513";
      ctx.lineWidth = 2;
      ctx.strokeRect(equipX - 10, equipY - 10, 200, 80);

      ctx.fillStyle = "#D2B48C";
      ctx.font = "bold 14px serif";
      ctx.textAlign = "left";
      ctx.fillText("장착 중:", equipX, equipY - 15);

      // 장비 슬롯들
      const equipSlots = [
        { type: "weapon", x: equipX, y: equipY, icon: "🐾", name: "발톱" },
        {
          type: "armor",
          x: equipX + slotSize + slotGap,
          y: equipY,
          icon: "🛡️",
          name: "털용품",
        },
        {
          type: "accessory",
          x: equipX + (slotSize + slotGap) * 2,
          y: equipY,
          icon: "💍",
          name: "장신구",
        },
      ];

      equipSlots.forEach((slot) => {
        const equippedItem =
          character.equipped[slot.type as keyof EquippedItems];

        // 슬롯 배경
        ctx.fillStyle = equippedItem
          ? "rgba(255, 215, 0, 0.2)"
          : "rgba(50, 50, 50, 0.5)";
        ctx.fillRect(slot.x, slot.y, slotSize, slotSize);
        ctx.strokeStyle = equippedItem ? "#FFD700" : "#666666";
        ctx.lineWidth = 2;
        ctx.strokeRect(slot.x, slot.y, slotSize, slotSize);

        if (equippedItem) {
          // 장착된 아이템 아이콘
          ctx.fillStyle = "#FFD700";
          ctx.font = "24px serif";
          ctx.textAlign = "center";
          ctx.fillText(
            equippedItem.icon || slot.icon,
            slot.x + slotSize / 2,
            slot.y + slotSize / 2 + 8
          );
        } else {
          // 빈 슬롯 아이콘
          ctx.fillStyle = "#666666";
          ctx.font = "20px serif";
          ctx.textAlign = "center";
          ctx.fillText(
            slot.icon,
            slot.x + slotSize / 2,
            slot.y + slotSize / 2 + 6
          );
        }
      });

      // 인벤토리 그리드
      const gridStartX = invX + 50;
      const gridStartY = invY + 240;
      const itemSize = 50;
      const itemGap = 5;
      const itemsPerRow = 10;
      const maxRows = 4;

      // 필터링된 아이템들
      const filteredItems = character.inventory.filter(
        (item) =>
          selectedInventoryTab === "all" || item.type === selectedInventoryTab
      );

      // 그리드 배경
      ctx.fillStyle = "rgba(60, 60, 60, 0.3)";
      ctx.fillRect(
        gridStartX - 5,
        gridStartY - 5,
        itemsPerRow * (itemSize + itemGap) - itemGap + 10,
        maxRows * (itemSize + itemGap) - itemGap + 10
      );

      filteredItems.forEach((item, index) => {
        const row = Math.floor(index / itemsPerRow);
        const col = index % itemsPerRow;

        if (row >= maxRows) return; // 최대 행 수 제한

        const itemX = gridStartX + col * (itemSize + itemGap);
        const itemY = gridStartY + row * (itemSize + itemGap);

        // 희귀도별 색상
        let rarityColor = "#D2B48C";
        switch (item.rarity) {
          case "common":
            rarityColor = "#D2B48C";
            break;
          case "uncommon":
            rarityColor = "#90EE90";
            break;
          case "rare":
            rarityColor = "#87CEEB";
            break;
          case "epic":
            rarityColor = "#DDA0DD";
            break;
          case "legendary":
            rarityColor = "#FFD700";
            break;
        }

        // 아이템 슬롯 배경
        ctx.fillStyle = "rgba(80, 60, 40, 0.8)";
        ctx.fillRect(itemX, itemY, itemSize, itemSize);

        // 희귀도 테두리
        ctx.strokeStyle = rarityColor;
        ctx.lineWidth = 2;
        ctx.strokeRect(itemX, itemY, itemSize, itemSize);

        // 아이템 아이콘
        ctx.fillStyle = rarityColor;
        ctx.font = "20px serif";
        ctx.textAlign = "center";
        ctx.fillText(
          item.icon || "📦",
          itemX + itemSize / 2,
          itemY + itemSize / 2 + 6
        );

        // 수량 표시
        if (item.quantity > 1) {
          ctx.fillStyle = "#FFFFFF";
          ctx.font = "10px Arial";
          ctx.textAlign = "right";
          ctx.fillText(
            item.quantity.toString(),
            itemX + itemSize - 5,
            itemY + itemSize - 5
          );
        }
      });

      // 정보 패널 (우측)
      const infoX = invX + 420;
      const infoY = invY + 140;
      const infoWidth = 250;
      const infoHeight = 280;

      ctx.fillStyle = "rgba(80, 60, 40, 0.8)";
      ctx.fillRect(infoX, infoY, infoWidth, infoHeight);
      ctx.strokeStyle = "#8B4513";
      ctx.lineWidth = 2;
      ctx.strokeRect(infoX, infoY, infoWidth, infoHeight);

      ctx.fillStyle = "#D2B48C";
      ctx.font = "bold 16px serif";
      ctx.textAlign = "left";
      ctx.fillText("캐릭터 정보", infoX + 10, infoY + 25);

      // 캐릭터 스탯 표시
      ctx.font = "14px serif";
      ctx.fillText(`레벨: ${character.level}`, infoX + 10, infoY + 50);
      ctx.fillText(
        `체력: ${Math.floor(character.health)}/${character.maxHealth}`,
        infoX + 10,
        infoY + 70
      );
      ctx.fillText(
        `마나: ${Math.floor(character.mana)}/${character.maxMana}`,
        infoX + 10,
        infoY + 90
      );
      ctx.fillText(`황금: ${character.gold}냥`, infoX + 10, infoY + 110);

      ctx.fillStyle = "#DAA520";
      ctx.font = "bold 14px serif";
      ctx.fillText("스탯:", infoX + 10, infoY + 140);

      ctx.fillStyle = "#D2B48C";
      ctx.font = "12px serif";
      ctx.fillText(`힘: ${character.stats.strength}`, infoX + 10, infoY + 160);
      ctx.fillText(`민첩: ${character.stats.agility}`, infoX + 10, infoY + 180);
      ctx.fillText(
        `체력: ${character.stats.vitality}`,
        infoX + 10,
        infoY + 200
      );
      ctx.fillText(
        `지능: ${character.stats.intelligence}`,
        infoX + 10,
        infoY + 220
      );

      // 아이템 툴팁
      if (hoveredItem) {
        const tooltipWidth = 250;
        const tooltipHeight = 120;
        const tooltipX = Math.min(
          canvas.width - tooltipWidth - 10,
          invX + invWidth + 10
        );
        const tooltipY = invY + 50;

        // 툴팁 배경
        ctx.fillStyle = "rgba(0, 0, 0, 0.9)";
        ctx.fillRect(tooltipX, tooltipY, tooltipWidth, tooltipHeight);
        ctx.strokeStyle =
          hoveredItem.rarity === "legendary"
            ? "#FFD700"
            : hoveredItem.rarity === "epic"
            ? "#DDA0DD"
            : hoveredItem.rarity === "rare"
            ? "#87CEEB"
            : hoveredItem.rarity === "uncommon"
            ? "#90EE90"
            : "#D2B48C";
        ctx.lineWidth = 2;
        ctx.strokeRect(tooltipX, tooltipY, tooltipWidth, tooltipHeight);

        // 아이템 이름
        ctx.fillStyle = ctx.strokeStyle;
        ctx.font = "bold 16px serif";
        ctx.textAlign = "left";
        ctx.fillText(
          `${hoveredItem.icon} ${hoveredItem.name}`,
          tooltipX + 10,
          tooltipY + 25
        );

        // 아이템 타입
        ctx.fillStyle = "#CCCCCC";
        ctx.font = "12px serif";
        const typeText =
          hoveredItem.type === "weapon"
            ? "무기"
            : hoveredItem.type === "armor"
            ? "털용품"
            : hoveredItem.type === "potion"
            ? "간식"
            : "기타";
        ctx.fillText(typeText, tooltipX + 10, tooltipY + 45);

        // 희귀도
        ctx.fillText(
          `희귀도: ${hoveredItem.rarity}`,
          tooltipX + 80,
          tooltipY + 45
        );

        // 설명
        ctx.fillStyle = "#FFFFFF";
        ctx.font = "11px serif";
        ctx.fillText(hoveredItem.description, tooltipX + 10, tooltipY + 65);

        // 스탯 정보
        if (hoveredItem.stats) {
          let statY = tooltipY + 85;
          if (hoveredItem.stats.attack) {
            ctx.fillStyle = "#FF6B6B";
            ctx.fillText(
              `⚔️ 공격력: +${hoveredItem.stats.attack}`,
              tooltipX + 10,
              statY
            );
            statY += 15;
          }
          if (hoveredItem.stats.defense) {
            ctx.fillStyle = "#4ECDC4";
            ctx.fillText(
              `🛡️ 방어력: +${hoveredItem.stats.defense}`,
              tooltipX + 10,
              statY
            );
            statY += 15;
          }
          if (hoveredItem.stats.health) {
            ctx.fillStyle = "#90EE90";
            ctx.fillText(
              `❤️ 체력 회복: +${hoveredItem.stats.health}`,
              tooltipX + 10,
              statY
            );
          }
        }
      }

      // 조작법
      ctx.fillStyle = "#DAA520";
      ctx.font = "12px serif";
      ctx.textAlign = "center";
      ctx.fillText(
        "ESC: 닫기 | 1~5: 탭 변경 | 좌클릭: 사용/장착 | 우클릭: 정보",
        invX + invWidth / 2,
        invY + invHeight - 20
      );
    }

    // 게임 클리어
    if (gameState === "gameClear") {
      ctx.fillStyle = "rgba(0, 0, 0, 0.9)";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.fillStyle = "#90EE90"; // 밝은 녹색
      ctx.font = "bold 48px serif";
      ctx.textAlign = "center";
      ctx.fillText("게임 클리어!", canvas.width / 2, canvas.height / 2 - 80);

      ctx.font = "28px serif";
      ctx.fillText(
        `${character.level}급 길냥이로 승리!`,
        canvas.width / 2,
        canvas.height / 2 - 30
      );
      ctx.fillText(
        `${character.gold}개의 밥 획득!`,
        canvas.width / 2,
        canvas.height / 2 + 10
      );

      ctx.font = "18px serif";
      ctx.fillText(
        "새로운 게임 시작 버튼을 눌러 다시 플레이하세요",
        canvas.width / 2,
        canvas.height / 2 + 60
      );
      ctx.textAlign = "left";
    }

    // 초기화 확인 대화 상자
    if (gameState === "confirmReset") {
      const dialogWidth = 400;
      const dialogHeight = 180;
      const dialogX = (canvas.width - dialogWidth) / 2;
      const dialogY = (canvas.height - dialogHeight) / 2;

      ctx.fillStyle = "rgba(244, 228, 188, 0.98)";
      ctx.fillRect(dialogX, dialogY, dialogWidth, dialogHeight);
      ctx.strokeStyle = "#2f1b14";
      ctx.lineWidth = 4;
      ctx.strokeRect(dialogX, dialogY, dialogWidth, dialogHeight);

      ctx.fillStyle = "#2f1b14";
      ctx.font = "bold 20px serif";
      ctx.textAlign = "center";
      ctx.fillText(
        "경고: 게임 초기화",
        dialogX + dialogWidth / 2,
        dialogY + 40
      );

      ctx.font = "16px serif";
      ctx.fillText(
        "정말 새로운 게임을 시작하시겠습니까?",
        dialogX + dialogWidth / 2,
        dialogY + 80
      );
      ctx.fillText(
        "현재 진행 상황은 모두 초기화됩니다.",
        dialogX + dialogWidth / 2,
        dialogY + 105
      );

      // 버튼
      const buttonWidth = 120;
      const buttonHeight = 40;
      const buttonY = dialogY + dialogHeight - 60;

      // 취소 버튼
      ctx.fillStyle = "#8b4513";
      ctx.fillRect(
        dialogX + dialogWidth / 2 - buttonWidth - 10,
        buttonY,
        buttonWidth,
        buttonHeight
      );
      ctx.strokeStyle = "#2f1b14";
      ctx.strokeRect(
        dialogX + dialogWidth / 2 - buttonWidth - 10,
        buttonY,
        buttonWidth,
        buttonHeight
      );
      ctx.fillStyle = "#ffffff";
      ctx.font = "bold 18px serif";
      ctx.fillText(
        "취소",
        dialogX + dialogWidth / 2 - buttonWidth / 2 - 10,
        buttonY + 27
      );

      // 확인 버튼
      ctx.fillStyle = "#dc143c";
      ctx.fillRect(
        dialogX + dialogWidth / 2 + 10,
        buttonY,
        buttonWidth,
        buttonHeight
      );
      ctx.strokeStyle = "#2f1b14";
      ctx.strokeRect(
        dialogX + dialogWidth / 2 + 10,
        buttonY,
        buttonWidth,
        buttonHeight
      );
      ctx.fillStyle = "#ffffff";
      ctx.font = "bold 18px serif";
      ctx.fillText(
        "확인",
        dialogX + dialogWidth / 2 + buttonWidth / 2 + 10,
        buttonY + 27
      );
    }
  }, [
    gameState,
    currentNPC,
    showInventory,
    canvasSize,
    drawCharacter,
    drawEnemy,
    drawNPC,
    drawOtherPlayer,
    onlinePlayers,
    user,
  ]);

  // 게임 루프
  useEffect(() => {
    const gameLoop = (currentTime: number) => {
      const deltaTime = currentTime - lastTimeRef.current;
      lastTimeRef.current = currentTime;

      updateGame(deltaTime);
      render();

      animationRef.current = requestAnimationFrame(gameLoop);
    };

    animationRef.current = requestAnimationFrame(gameLoop);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [updateGame, render]);

  // 초기화
  useEffect(() => {
    generateWorld();
    generateNPCs();
  }, [generateWorld, generateNPCs]);

  // 캔버스 크기 업데이트
  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      canvas.width = canvasSize.width;
      canvas.height = canvasSize.height;
    }
  }, [canvasSize]);

  const resetGame = async () => {
    characterRef.current = {
      pos: {
        x: (WORLD_WIDTH * TILE_SIZE) / 2,
        y: (WORLD_HEIGHT * TILE_SIZE) / 2,
      },
      vel: { x: 0, y: 0 },
      size: { x: 24, y: 32 },
      health: 100,
      maxHealth: 100,
      mana: 50,
      maxMana: 50,
      level: 1,
      exp: 0,
      expToNext: 100,
      stats: {
        strength: 10,
        agility: 8,
        vitality: 12,
        intelligence: 6,
      },
      state: "idle",
      direction: 1,
      animFrame: 0,
      animTimer: 0,
      attackTimer: 0,
      weapon: "sword",
      inventory: [
        {
          id: "sword1",
          name: "철검",
          type: "weapon",
          quantity: 1,
          description: "튼튼한 철로 만든 검",
          icon: "⚔️",
          rarity: "common",
          stats: { attack: 15 },
        },
        {
          id: "potion1",
          name: "체력 물약",
          type: "potion",
          quantity: 3,
          description: "체력을 회복하는 물약",
          icon: "🧪",
          rarity: "common",
          stats: { health: 50 },
        },
        {
          id: "leather_armor",
          name: "가죽 갑옷",
          type: "armor",
          quantity: 1,
          description: "가벼운 가죽으로 만든 갑옷",
          icon: "🛡️",
          rarity: "common",
          stats: { defense: 8 },
        },
      ],
      gold: 50,
      invulnerable: 0,
      equipped: {
        weapon: {
          id: "sword1",
          name: "철검",
          type: "weapon",
          quantity: 1,
          description: "튼튼한 철로 만든 검",
          icon: "⚔️",
          rarity: "common",
          stats: { attack: 15 },
        },
      },
    };
    enemiesRef.current = [];
    particlesRef.current = [];
    setGameState("playing");
    setCurrentNPC(null);
    setShowInventory(false);
    setSelectedInventoryTab("all");
    setHoveredItem(null);

    // Firebase에 새로운 게임 데이터 저장
    if (user) {
      await saveToFirebase(characterRef.current);
    }
  };

  const handleNewGameClick = () => {
    setGameState("confirmReset");
  };

  const handleConfirmReset = (confirm: boolean) => {
    if (confirm) {
      resetGame();
    } else {
      setGameState("playing");
    }
  };

  // 캔버스 클릭 이벤트 (확인 대화 상자 버튼 처리)
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const handleClick = (event: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;

      if (gameState === "confirmReset") {
        const dialogWidth = 400;
        const dialogHeight = 180;
        const dialogX = (canvas.width - dialogWidth) / 2;
        const dialogY = (canvas.height - dialogHeight) / 2;

        const buttonWidth = 120;
        const buttonHeight = 40;
        const buttonY = dialogY + dialogHeight - 60;

        // 취소 버튼 영역
        const cancelBtnX = dialogX + dialogWidth / 2 - buttonWidth - 10;
        const cancelBtnY = buttonY;
        if (
          mouseX >= cancelBtnX &&
          mouseX <= cancelBtnX + buttonWidth &&
          mouseY >= cancelBtnY &&
          mouseY <= cancelBtnY + buttonHeight
        ) {
          handleConfirmReset(false);
          return;
        }

        // 확인 버튼 영역
        const confirmBtnX = dialogX + dialogWidth / 2 + 10;
        const confirmBtnY = buttonY;
        if (
          mouseX >= confirmBtnX &&
          mouseX <= confirmBtnX + buttonWidth &&
          mouseY >= confirmBtnY &&
          mouseY <= confirmBtnY + buttonHeight
        ) {
          handleConfirmReset(true);
          return;
        }
      }

      // 인벤토리 클릭 처리
      if (showInventory) {
        const invWidth = 700;
        const invHeight = 400;
        const invX = (canvas.width - invWidth) / 2;
        const invY = (canvas.height - invHeight) / 2;

        // 탭 클릭 처리
        const tabs = ["all", "weapon", "armor", "potion", "misc"];
        const tabWidth = 100;
        const tabHeight = 40;
        const tabStartX = invX + 50;

        for (let i = 0; i < tabs.length; i++) {
          const tabX = tabStartX + i * (tabWidth + 10);
          const tabY = invY + 85;

          if (
            mouseX >= tabX &&
            mouseX <= tabX + tabWidth &&
            mouseY >= tabY &&
            mouseY <= tabY + tabHeight
          ) {
            setSelectedInventoryTab(
              tabs[i] as "all" | "weapon" | "armor" | "potion" | "misc"
            );
            return;
          }
        }

        // 장비 슬롯 클릭 처리 (장착 해제)
        const equipX = invX + 50;
        const equipY = invY + 140;
        const slotSize = 60;
        const slotGap = 10;

        const equipSlots = [
          { type: "weapon", x: equipX, y: equipY },
          { type: "armor", x: equipX + slotSize + slotGap, y: equipY },
          {
            type: "accessory",
            x: equipX + (slotSize + slotGap) * 2,
            y: equipY,
          },
        ];

        for (const slot of equipSlots) {
          if (
            mouseX >= slot.x &&
            mouseX <= slot.x + slotSize &&
            mouseY >= slot.y &&
            mouseY <= slot.y + slotSize
          ) {
            if (event.button === 2) {
              // 우클릭으로 장착 해제
              unequipItem(slot.type as keyof EquippedItems);
            }
            return;
          }
        }

        // 아이템 그리드 클릭 처리
        const gridStartX = invX + 50;
        const gridStartY = invY + 240;
        const itemSize = 50;
        const itemGap = 5;
        const itemsPerRow = 10;

        const filteredItems = characterRef.current.inventory.filter(
          (item) =>
            selectedInventoryTab === "all" || item.type === selectedInventoryTab
        );

        for (let i = 0; i < filteredItems.length; i++) {
          const row = Math.floor(i / itemsPerRow);
          const col = i % itemsPerRow;

          if (row >= 4) break; // 최대 행 수 제한

          const itemX = gridStartX + col * (itemSize + itemGap);
          const itemY = gridStartY + row * (itemSize + itemGap);

          if (
            mouseX >= itemX &&
            mouseX <= itemX + itemSize &&
            mouseY >= itemY &&
            mouseY <= itemY + itemSize
          ) {
            const clickedItem = filteredItems[i];

            if (event.button === 0) {
              // 좌클릭으로 아이템 사용/장착
              useItem(clickedItem);
            } else if (event.button === 2) {
              // 우클릭으로 아이템 정보 표시
              setHoveredItem(clickedItem);
            }
            return;
          }
        }
      } else {
        // 인벤토리 밖을 클릭했을 때 툴팁 제거
        setHoveredItem(null);
      }
    };

    const handleContextMenu = (event: MouseEvent) => {
      event.preventDefault(); // 우클릭 컨텍스트 메뉴 방지
    };

    canvas.addEventListener("click", handleClick);
    canvas.addEventListener("mousedown", handleClick); // 마우스다운 이벤트로 우클릭도 처리
    canvas.addEventListener("contextmenu", handleContextMenu);

    return () => {
      canvas.removeEventListener("click", handleClick);
      canvas.removeEventListener("mousedown", handleClick);
      canvas.removeEventListener("contextmenu", handleContextMenu);
    };
  }, [gameState, canvasSize]); // canvasSize를 의존성 배열에 추가

  return (
    <div className="w-screen h-screen bg-black overflow-hidden">
      {/* 상단 게임 인터페이스 */}
      {(user || isGuest) && characterRef.current && (
        <GameInterface
          character={characterRef.current}
          onlineCount={isGuest ? 1 : onlinePlayers.length + 1}
          enemyCount={enemiesRef.current.length}
          tileSize={TILE_SIZE}
          isGuest={isGuest}
          worldMap={worldMapRef.current}
          worldWidth={WORLD_WIDTH}
          worldHeight={WORLD_HEIGHT}
          enemies={enemiesRef.current}
        />
      )}

      <canvas
        ref={canvasRef}
        width={canvasSize.width}
        height={canvasSize.height}
        className="block mt-16"
        tabIndex={0}
        style={{ cursor: "none" }}
      />

      {/* 게임 컨트롤 (우상단) */}
      <div className="absolute top-20 right-4 z-10">
        <Button
          onClick={handleNewGameClick}
          className="bg-red-800 hover:bg-red-900 text-white font-bold px-4 py-2 text-sm"
          disabled={
            gameState === "dialogue" ||
            gameState === "inventory" ||
            gameState === "gameOver" ||
            gameState === "gameClear"
          }
        >
          새로운 냥생 시작
        </Button>
      </div>

      {/* 인벤토리 버튼 (하단 우측) */}
      {(user || isGuest) && (
        <div className="absolute bottom-72 right-4 z-30">
          <Button
            onClick={() => setShowInventory(!showInventory)}
            className={`${
              showInventory
                ? "bg-amber-600 hover:bg-amber-700"
                : "bg-slate-700 hover:bg-slate-600"
            } text-white font-bold px-4 py-3 text-sm rounded-xl shadow-lg transition-all duration-200 hover:scale-105`}
            disabled={
              gameState === "dialogue" ||
              gameState === "gameOver" ||
              gameState === "gameClear"
            }
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">🧳</span>
              <span>냥용품 (I)</span>
            </div>
          </Button>
        </div>
      )}

      {/* 채팅 시스템 - 하단 고정 */}
      {(user || isGuest) && (
        <ChatSystem
          user={user}
          isGuest={isGuest}
          displayName={
            isGuest
              ? guestName
              : user?.displayName ||
                user?.email?.split("@")[0] ||
                "무명의 길냥이"
          }
          characterLevel={characterRef.current?.level || 1}
          isVisible={isChatVisible}
          onlineCount={isGuest ? 1 : onlinePlayers.length + 1}
          onToggle={() => setIsChatVisible(!isChatVisible)}
        />
      )}
    </div>
  );
}
