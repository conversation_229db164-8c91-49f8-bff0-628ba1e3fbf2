"use client";

import { LoginForm } from "@/components/LoginForm";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import JoseonRPGImproved from "@/joseon-rpg-improved";
import { getUserProfile, loadGameData } from "@/lib/gameData";
import { useEffect, useState } from "react";

export default function OnlineGameWrapper() {
  const { user, isGuest, guestName, loading, logout, signInAsGuest } =
    useAuth();
  const [showLogin, setShowLogin] = useState(false);
  const [gameData, setGameData] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loadingGameData, setLoadingGameData] = useState(false);

  useEffect(() => {
    if (user && !gameData) {
      loadUserData();
    } else if (isGuest && !gameData) {
      // 게스트용 기본 게임 데이터
      setGameData({
        level: 1,
        experience: 0,
        experienceToNext: 100,
        health: 100,
        maxHealth: 100,
        mana: 50,
        maxMana: 50,
        gold: 100,
        attack: 10,
        defense: 5,
        inventory: [],
        equipped: {},
        position: { x: 2400, y: 2400 },
        lastSaved: new Date(),
      });
    }
  }, [user, isGuest]);

  const loadUserData = async () => {
    if (!user) return;

    setLoadingGameData(true);
    try {
      const [profile, game] = await Promise.all([
        getUserProfile(user),
        loadGameData(user),
      ]);

      setUserProfile(profile);
      setGameData(game);
    } catch (error) {
      console.error("Error loading user data:", error);
    } finally {
      setLoadingGameData(false);
    }
  };

  const handleLogout = async () => {
    await logout();
    setGameData(null);
    setUserProfile(null);
  };

  // 로딩 중
  if (loading || loadingGameData) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-amber-100 to-amber-200 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😺</div>
          <h1 className="text-3xl font-bold text-amber-900 mb-2">
            😺 냥바람의나라
          </h1>
          <p className="text-amber-700">게임 데이터를 불러오는 중...</p>
          <div className="mt-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  // 로그인하지 않은 상태 (게스트 포함하지 않음)
  if (!user && !isGuest) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-amber-100 to-amber-200 flex items-center justify-center">
        <div className="text-center max-w-2xl mx-auto px-4">
          <div className="text-8xl mb-6">😺</div>
          <h1 className="text-5xl font-bold text-amber-900 mb-4">
            😺 냥바람의나라
          </h1>
          <p className="text-xl text-amber-800 mb-8">
            고양이들만의 세계에서 전설의 길냥이가 되어보세요!
          </p>

          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white bg-opacity-70 p-4 rounded-lg">
              <div className="text-3xl mb-2">🐾</div>
              <h3 className="font-bold text-amber-900 mb-1">냥냥 전투</h3>
              <p className="text-sm text-amber-700">
                냥펀치, 할퀴기 등 고양이만의 기술
              </p>
            </div>
            <div className="bg-white bg-opacity-70 p-4 rounded-lg">
              <div className="text-3xl mb-2">🍚</div>
              <h3 className="font-bold text-amber-900 mb-1">밥 수집</h3>
              <p className="text-sm text-amber-700">
                츄르, 캔사료를 모으며 성장하세요
              </p>
            </div>
            <div className="bg-white bg-opacity-70 p-4 rounded-lg">
              <div className="text-3xl mb-2">😸</div>
              <h3 className="font-bold text-amber-900 mb-1">냥친구들</h3>
              <p className="text-sm text-amber-700">
                다른 고양이들과 함께 영역 정복
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <Button
              onClick={() => setShowLogin(true)}
              className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-3 text-lg font-bold rounded-lg shadow-lg w-full"
            >
              🐱 로그인하고 냥생 시작
            </Button>

            <div className="flex items-center gap-4">
              <div className="flex-1 h-px bg-amber-300"></div>
              <span className="text-amber-700 font-medium">또는</span>
              <div className="flex-1 h-px bg-amber-300"></div>
            </div>

            <Button
              onClick={signInAsGuest}
              variant="outline"
              className="border-amber-500 text-amber-700 hover:bg-amber-50 px-8 py-3 text-lg font-bold rounded-lg shadow-lg w-full"
            >
              🐾 게스트 냥이로 체험하기
            </Button>
          </div>

          <div className="mt-6 space-y-2">
            <div className="text-sm text-amber-600">
              * 무료로 플레이할 수 있습니다 *
            </div>
            <div className="text-xs text-amber-500">
              💡 게스트는 진행 상황이 저장되지 않습니다
            </div>
          </div>
        </div>

        {showLogin && <LoginForm onClose={() => setShowLogin(false)} />}
      </div>
    );
  }

  // 로그인한 상태 또는 게스트 - 게임 실행
  return (
    <div className="relative">
      {/* 상단 사용자 정보 바 */}
      <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 text-white p-2 flex justify-between items-center z-50">
        <div className="flex items-center space-x-4">
          <span className="text-sm">
            🏯{" "}
            <strong>
              {isGuest
                ? `${guestName} (게스트)`
                : userProfile?.displayName ||
                  user?.displayName ||
                  "무명의 무사"}
            </strong>
          </span>
          {gameData && (
            <span className="text-sm opacity-75">
              레벨 {gameData.level} | 밥 {gameData.gold}개
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleLogout}
            variant="outline"
            size="sm"
            className="text-xs bg-transparent border-white text-white hover:bg-white hover:text-black"
          >
            로그아웃
          </Button>
        </div>
      </div>

      {/* 게임 컴포넌트 */}
      <JoseonRPGImproved
        user={user}
        isGuest={isGuest}
        guestName={guestName}
        initialGameData={gameData}
        onGameDataChange={(data) => setGameData(data)}
      />
    </div>
  );
}
