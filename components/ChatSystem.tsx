"use client";

import {
  ChatMessage,
  sendChatMessage,
  subscribeToChatMessages,
} from "@/lib/gameData";
import { User } from "firebase/auth";
import { useEffect, useRef, useState } from "react";
import { Button } from "./ui/button";

interface ChatSystemProps {
  user: User | null;
  isGuest?: boolean;
  displayName: string;
  characterLevel: number;
  isVisible: boolean;
  onlineCount?: number;
  onToggle: () => void;
}

export default function ChatSystem({
  user,
  isGuest = false,
  displayName,
  characterLevel,
  isVisible,
  onlineCount = 1,
  onToggle,
}: ChatSystemProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 채팅 메시지 실시간 구독
  useEffect(() => {
    if (!user && !isGuest) return;

    let unsubscribe: (() => void) | null = null;

    try {
      unsubscribe = subscribeToChatMessages((newMessages) => {
        setMessages(newMessages);
      });
    } catch (error) {
      console.error("Error subscribing to chat messages:", error);
      setMessages([]);
    }

    return () => {
      if (unsubscribe) {
        try {
          unsubscribe();
        } catch (error) {
          console.error("Error unsubscribing from chat messages:", error);
        }
      }
    };
  }, [user]);

  // 새 메시지가 올 때마다 스크롤을 아래로
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = async () => {
    if ((!user && !isGuest) || !inputMessage.trim() || isSending) return;

    // 게스트인 경우 가짜 사용자 객체 생성
    if (isGuest) {
      // 게스트는 실제 Firebase에 저장하지 않고 로컬에서만 메시지 표시
      const guestMessage = {
        id: `guest-${Date.now()}`,
        message: inputMessage,
        displayName: displayName,
        characterLevel: characterLevel,
        timestamp: new Date(),
        userId: "guest",
      };

      // 로컬 메시지 목록에 추가
      setMessages((prev) => [...prev, guestMessage]);
      setInputMessage("");
      return;
    }

    setIsSending(true);

    const success = await sendChatMessage(
      user,
      inputMessage,
      displayName,
      characterLevel
    );

    if (success) {
      setInputMessage("");
    }

    setIsSending(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: any) => {
    if (!timestamp) return "";

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleTimeString("ko-KR", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getLevelColor = (level: number) => {
    if (level >= 50) return "text-purple-400";
    if (level >= 30) return "text-blue-400";
    if (level >= 20) return "text-green-400";
    if (level >= 10) return "text-yellow-400";
    return "text-gray-400";
  };

  // 우측 채팅창 - 최소화 가능

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={onToggle}
          className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 text-white px-6 py-3 rounded-2xl shadow-2xl transform transition-all duration-200 hover:scale-105 backdrop-blur-sm border border-white/20"
        >
          <div className="flex items-center gap-2">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                clipRule="evenodd"
              />
            </svg>
            채팅 ({messages.length})
          </div>
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-40 w-80 h-64 bg-slate-900/95 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl transform transition-all duration-300 hover:scale-105">
      {/* 채팅 헤더 */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-white/10 bg-gradient-to-r from-amber-600/20 to-amber-700/20 rounded-t-2xl">
        <div className="flex items-center gap-3">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <h3 className="text-white font-semibold text-sm">
            😺 냥바람의나라 채팅
          </h3>
          <span className="text-xs text-white/60 bg-white/10 px-2 py-1 rounded-full">
            LIVE
          </span>
          {isGuest && (
            <span className="text-xs text-amber-300 bg-amber-600/20 px-2 py-1 rounded-full">
              게스트
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-white/60">온라인: {onlineCount}명</span>
          <button
            onClick={onToggle}
            className="text-white/60 hover:text-white w-6 h-6 flex items-center justify-center rounded hover:bg-white/10 transition-all duration-200"
            title="채팅창 최소화"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M20 12H4"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* 메시지 목록 */}
      <div className="flex-1 overflow-y-auto px-4 py-2 h-36 space-y-1">
        {messages.length === 0 ? (
          <div className="text-center mt-4">
            <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-r from-amber-500/20 to-amber-600/20 rounded-full flex items-center justify-center">
              <svg
                className="w-6 h-6 text-white/40"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <p className="text-white/60 text-sm">채팅방이 조용하네요 🏯</p>
            <p className="text-white/40 text-xs mt-1">
              첫 메시지를 보내서 다른 무사들과 소통해보세요! ⚔️
            </p>
          </div>
        ) : (
          messages.map((msg) => (
            <div key={msg.id} className="group">
              <div className="flex items-center gap-2 mb-1">
                <div className="flex items-center gap-1">
                  <div
                    className={`w-2 h-2 rounded-full ${getLevelColor(
                      msg.characterLevel || 1
                    ).replace("text-", "bg-")}`}
                  ></div>
                  <span
                    className={`font-semibold text-xs ${getLevelColor(
                      msg.characterLevel || 1
                    )}`}
                  >
                    Lv.{msg.characterLevel || 1}
                  </span>
                  <span className="font-medium text-white text-sm">
                    {msg.displayName}
                  </span>
                </div>
                <span className="text-white/40 text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                  {formatTime(msg.timestamp)}
                </span>
              </div>
              <div className="bg-white/5 backdrop-blur-sm rounded-lg px-3 py-2 ml-3">
                <div className="text-white/90 text-sm break-words leading-relaxed">
                  {msg.message}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 메시지 입력 */}
      <div className="px-4 py-3 border-t border-white/10 bg-gradient-to-r from-slate-800/50 to-slate-900/50 rounded-b-2xl">
        <div className="flex gap-3 items-center">
          <div className="flex-1">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={
                isGuest
                  ? "게스트로 채팅 중... (저장되지 않음)"
                  : "메시지를 입력하세요..."
              }
              disabled={isSending}
              className="w-full bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-white/50 px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-200"
              maxLength={200}
            />
          </div>
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isSending}
            className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 disabled:from-gray-600 disabled:to-gray-600 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-lg"
          >
            {isSending ? (
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
            ) : (
              <div className="flex items-center gap-1">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                  />
                </svg>
                <span className="text-xs font-medium">전송</span>
              </div>
            )}
          </Button>
        </div>
        <div className="flex justify-between items-center mt-2">
          <span className="text-white/40 text-xs">
            Enter로 전송 • {inputMessage.length}/200
          </span>
          <div className="flex items-center gap-2">
            {isGuest && (
              <span className="text-amber-400 text-xs">👤 게스트 모드</span>
            )}
            <div className="flex items-center gap-1 text-white/40 text-xs">
              <svg
                className="w-3 h-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
              실시간
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
