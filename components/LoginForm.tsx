"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import React, { useState } from "react";

interface LoginFormProps {
  onClose: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({ onClose }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [displayName, setDisplayName] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // 컴포넌트 마운트 시 초기화
  React.useEffect(() => {
    // 페이지 로드 시 loading 상태 초기화
    setLoading(false);
    setError("");

    // 스피너 애니메이션 CSS 추가
    const style = document.createElement("style");
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);

    return () => {
      // 컴포넌트 언마운트 시 스타일 제거
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  const { signIn, signUp, signInWithGoogle } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      if (isLogin) {
        await signIn(email, password);
      } else {
        await signUp(email, password, displayName);
      }
      onClose();
    } catch (error: any) {
      setError(getErrorMessage(error.code));
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    if (loading) return;

    setLoading(true);
    setError("");

    try {
      await signInWithGoogle();
      onClose();
    } catch (error: any) {
      if (error.code === "auth/popup-blocked") {
        setError(
          "팝업이 차단되었습니다. 브라우저 설정에서 팝업을 허용해주세요."
        );
      } else if (error.code === "auth/popup-closed-by-user") {
        setError("로그인이 취소되었습니다.");
      } else if (error.code === "auth/configuration-not-found") {
        setError(
          "구글 로그인이 설정되지 않았습니다. Firebase Console에서 Google 인증을 활성화해주세요."
        );
      } else {
        setError(getErrorMessage(error.code));
      }
    } finally {
      setLoading(false);
    }
  };

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case "auth/user-not-found":
        return "등록되지 않은 이메일입니다.";
      case "auth/wrong-password":
        return "비밀번호가 올바르지 않습니다.";
      case "auth/email-already-in-use":
        return "이미 사용 중인 이메일입니다.";
      case "auth/weak-password":
        return "비밀번호는 6자 이상이어야 합니다.";
      case "auth/invalid-email":
        return "유효하지 않은 이메일 형식입니다.";
      default:
        return "로그인 중 오류가 발생했습니다.";
    }
  };

  return (
    <div className="fixed inset-0 bg-gradient-to-b from-amber-100 to-amber-200 flex items-center justify-center z-50">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(251,191,36,0.1),transparent_50%)]"></div>
      </div>

      <div className="relative bg-white bg-opacity-80 backdrop-blur-xl rounded-2xl p-8 max-w-md w-full mx-4 border border-amber-200 shadow-2xl">
        {/* Soft Traditional Glow */}
        <div className="absolute -inset-1 bg-gradient-to-r from-amber-300 via-orange-300 to-amber-400 rounded-2xl blur opacity-30 animate-pulse"></div>

        <div className="relative text-center mb-8">
          <div className="inline-flex items-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-r from-amber-600 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">😺</span>
            </div>
            <h2 className="text-3xl font-bold text-amber-900">
              😺 냥바람의나라
            </h2>
          </div>

          {/* 로그인/회원가입 탭 */}
          <div className="flex bg-amber-100 rounded-lg p-1 mb-4">
            <button
              onClick={() => setIsLogin(true)}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
                isLogin
                  ? "bg-white text-amber-900 shadow-sm"
                  : "text-amber-700 hover:text-amber-900"
              }`}
            >
              ⚔️ 로그인
            </button>
            <button
              onClick={() => setIsLogin(false)}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
                !isLogin
                  ? "bg-white text-amber-900 shadow-sm"
                  : "text-amber-700 hover:text-amber-900"
              }`}
            >
              🏯 회원가입
            </button>
          </div>

          <p className="text-amber-700 text-sm mb-3">
            {isLogin
              ? "기존 계정으로 로그인하세요"
              : "🎉 새로운 계정을 만들어 무사가 되어보세요!"}
          </p>

          {!isLogin && (
            <div className="mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
              <div className="text-xs text-green-800">
                <strong>✨ 회원가입 혜택:</strong>
                <br />
                • 무료로 냥바람의나라 플레이
                <br />
                • 클라우드 저장으로 어디서든 게임 진행
                <br />• 온라인 멀티플레이어 지원
              </div>
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="relative space-y-6">
          <div>
            <label className="block text-sm font-medium text-amber-800 mb-2">
              이메일
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-amber-300 rounded-xl text-amber-900 placeholder-amber-500 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-200"
              placeholder="이메일을 입력하세요"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-amber-800 mb-2">
              비밀번호
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-amber-300 rounded-xl text-amber-900 placeholder-amber-500 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-200"
              placeholder="비밀번호를 입력하세요"
              required
            />
          </div>

          {!isLogin && (
            <div>
              <label className="block text-sm font-medium text-amber-800 mb-2">
                플레이어명
              </label>
              <input
                type="text"
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                className="w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-amber-300 rounded-xl text-amber-900 placeholder-amber-500 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent transition-all duration-200"
                placeholder="게임에서 사용할 이름"
                required
              />
            </div>
          )}

          {error && (
            <div className="bg-red-100 border border-red-300 rounded-xl p-3 text-red-700 text-sm text-center">
              {error}
            </div>
          )}

          <Button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white py-3 px-6 rounded-xl font-semibold transform transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:scale-100 shadow-lg"
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                처리 중...
              </div>
            ) : isLogin ? (
              "⚔️ 로그인"
            ) : (
              "🏯 회원가입 완료"
            )}
          </Button>
        </form>

        <div className="relative mt-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-amber-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-4 bg-white text-amber-600">또는</span>
          </div>
        </div>

        <div className="mt-6">
          {/* 작동하는 순수 HTML 구글 버튼 */}
          <div
            onClick={(e) => {
              console.log("🚀 Google 버튼 클릭됨!");
              e.preventDefault();
              e.stopPropagation();
              if (!loading) {
                handleGoogleSignIn();
              }
            }}
            style={{
              width: "100%",
              padding: "12px 20px",
              backgroundColor: loading ? "#f3f4f6" : "rgba(255, 255, 255, 0.8)",
              color: loading ? "#9ca3af" : "#92400e",
              border: loading ? "2px solid #d1d5db" : "2px solid #f59e0b",
              borderRadius: "12px",
              cursor: loading ? "not-allowed" : "pointer",
              textAlign: "center",
              fontSize: "16px",
              fontWeight: "600",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: "12px",
              transition: "all 0.2s ease",
              boxShadow: loading ? "none" : "0 4px 6px rgba(0, 0, 0, 0.1)",
              position: "relative",
              zIndex: 99999,
              userSelect: "none",
              WebkitUserSelect: "none",
              MozUserSelect: "none",
            }}
            onMouseEnter={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor =
                  "rgba(255, 255, 255, 1)";
                e.currentTarget.style.transform = "scale(1.02)";
                e.currentTarget.style.boxShadow =
                  "0 6px 12px rgba(0, 0, 0, 0.15)";
              }
            }}
            onMouseLeave={(e) => {
              if (!loading) {
                e.currentTarget.style.backgroundColor =
                  "rgba(255, 255, 255, 0.8)";
                e.currentTarget.style.transform = "scale(1)";
                e.currentTarget.style.boxShadow =
                  "0 4px 6px rgba(0, 0, 0, 0.1)";
              }
            }}
          >
            {loading && (
              <div
                style={{
                  width: "18px",
                  height: "18px",
                  border: "3px solid #d1d5db",
                  borderTop: "3px solid #92400e",
                  borderRadius: "50%",
                  animation: "spin 1s linear infinite",
                }}
              ></div>
            )}

            {/* Google 아이콘 */}
            <svg
              style={{ width: "20px", height: "20px", flexShrink: 0 }}
              viewBox="0 0 24 24"
            >
              <path
                fill="#4285F4"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="#34A853"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="#FBBC05"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="#EA4335"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>

            <span
              style={{
                fontWeight: "600",
                fontSize: "16px",
                color: loading ? "#9ca3af" : "#92400e",
              }}
            >
              {loading
                ? "처리 중..."
                : isLogin
                ? "Google로 로그인"
                : "Google로 회원가입"}
            </span>
          </div>

          {/* 처음 사용자를위한 빠른 회원가입 버튼 */}
          {isLogin && (
            <div className="mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
              <div className="text-center">
                <div className="text-2xl mb-2">🆕</div>
                <h3 className="font-bold text-green-800 mb-1">
                  처음 오셨나요?
                </h3>
                <p className="text-xs text-green-700 mb-3">
                  30초만에 무료 계정을 만들고 바로 게임 시작!
                </p>
                <button
                  onClick={() => setIsLogin(false)}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                >
                  🚀 무료 회원가입하기
                </button>
              </div>
            </div>
          )}
        </div>

        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-amber-600 hover:text-amber-800 text-2xl transition-colors duration-200 w-8 h-8 flex items-center justify-center"
        >
          ×
        </button>
      </div>
    </div>
  );
};
