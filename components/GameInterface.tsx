"use client";
import { useEffect, useRef } from "react";

interface GameInterfaceProps {
  character: {
    level: number;
    health: number;
    maxHealth: number;
    mana: number;
    maxMana: number;
    exp: number;
    expToNext: number;
    gold: number;
    stats: {
      strength: number;
      agility: number;
      vitality: number;
      intelligence: number;
    };
    pos: { x: number; y: number };
    equipped: {
      weapon?: { name: string };
      armor?: { name: string };
    };
  };
  onlineCount: number;
  enemyCount: number;
  tileSize: number;
  isGuest?: boolean;
  worldMap?: any[][];
  worldWidth?: number;
  worldHeight?: number;
  enemies?: Array<{ pos: { x: number; y: number } }>;
}

export default function GameInterface({
  character,
  onlineCount,
  enemyCount,
  tileSize,
  isGuest = false,
  worldMap,
  worldWidth = 150,
  worldHeight = 150,
  enemies = [],
}: GameInterfaceProps) {
  const healthPercent = (character.health / character.maxHealth) * 100;
  const manaPercent = (character.mana / character.maxMana) * 100;
  const expPercent = (character.exp / character.expToNext) * 100;

  const minimapCanvasRef = useRef<HTMLCanvasElement>(null);

  // 미니맵 그리기
  useEffect(() => {
    const canvas = minimapCanvasRef.current;
    if (!canvas || !worldMap) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const minimapSize = 100;
    canvas.width = minimapSize;
    canvas.height = minimapSize;

    // 배경
    ctx.fillStyle = "rgba(0, 0, 0, 0.8)";
    ctx.fillRect(0, 0, minimapSize, minimapSize);

    // 타일들
    const minimapScale = minimapSize / (worldWidth * tileSize);
    for (let y = 0; y < worldHeight; y += 4) {
      for (let x = 0; x < worldWidth; x += 4) {
        if (worldMap[y] && worldMap[y][x]) {
          const tile = worldMap[y][x];
          ctx.fillStyle = tile.color;
          ctx.fillRect(
            x * tileSize * minimapScale,
            y * tileSize * minimapScale,
            4 * tileSize * minimapScale,
            4 * tileSize * minimapScale
          );
        }
      }
    }

    // 적들 위치
    ctx.fillStyle = "#ff4444";
    enemies.forEach((enemy) => {
      ctx.beginPath();
      ctx.arc(
        enemy.pos.x * minimapScale,
        enemy.pos.y * minimapScale,
        1.5,
        0,
        Math.PI * 2
      );
      ctx.fill();
    });

    // 플레이어 위치 (적들보다 위에 그려서 더 잘 보이게)
    ctx.fillStyle = "#ff0000";
    ctx.beginPath();
    ctx.arc(
      character.pos.x * minimapScale,
      character.pos.y * minimapScale,
      2,
      0,
      Math.PI * 2
    );
    ctx.fill();

    // 테두리
    ctx.strokeStyle = "#ffffff";
    ctx.lineWidth = 2;
    ctx.strokeRect(0, 0, minimapSize, minimapSize);
  }, [character.pos, worldMap, worldWidth, worldHeight, tileSize, enemies]);

  return (
    <div className="fixed top-0 left-0 right-0 z-20 bg-gradient-to-r from-slate-900/95 to-slate-800/95 backdrop-blur-xl border-b border-amber-500/30 shadow-2xl">
      <div className="flex items-center justify-between px-6 py-4 gap-8">
        {/* 좌측: 캐릭터 정보 */}
        <div className="flex items-center gap-6">
          {/* 레벨 및 직업 */}
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-r from-amber-600 to-amber-700 rounded-xl flex items-center justify-center">
              <span className="text-xl">🐱</span>
            </div>
            <div>
              <h2 className="text-white font-bold text-lg">
                Lv.{character.level} 길냥이
              </h2>
              {isGuest && (
                <span className="text-amber-300 text-xs bg-amber-600/20 px-2 py-1 rounded-full">
                  게스트
                </span>
              )}
            </div>
          </div>

          {/* 상태 바들 */}
          <div className="flex flex-col gap-2 min-w-80">
            {/* 체력바 */}
            <div className="flex items-center gap-3">
              <span className="text-red-400 text-sm font-semibold w-8">HP</span>
              <div className="flex-1 bg-gray-700 rounded-full h-3 overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-red-500 to-red-400 transition-all duration-300"
                  style={{ width: `${healthPercent}%` }}
                ></div>
              </div>
              <span className="text-white text-sm font-medium w-16 text-right">
                {Math.floor(character.health)}/{character.maxHealth}
              </span>
            </div>

            {/* 마나바 */}
            <div className="flex items-center gap-3">
              <span className="text-blue-400 text-sm font-semibold w-8">
                MP
              </span>
              <div className="flex-1 bg-gray-700 rounded-full h-3 overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-blue-500 to-blue-400 transition-all duration-300"
                  style={{ width: `${manaPercent}%` }}
                ></div>
              </div>
              <span className="text-white text-sm font-medium w-16 text-right">
                {Math.floor(character.mana)}/{character.maxMana}
              </span>
            </div>

            {/* 경험치바 */}
            <div className="flex items-center gap-3">
              <span className="text-purple-400 text-sm font-semibold w-8">
                XP
              </span>
              <div className="flex-1 bg-gray-700 rounded-full h-3 overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-purple-500 to-purple-400 transition-all duration-300"
                  style={{ width: `${expPercent}%` }}
                ></div>
              </div>
              <span className="text-white text-sm font-medium w-16 text-right">
                {character.exp}/{character.expToNext}
              </span>
            </div>
          </div>
        </div>

        {/* 중앙: 게임 정보 */}
        <div className="flex items-center gap-6">
          {/* 골드 */}
          <div className="flex items-center gap-2 bg-yellow-600/20 px-4 py-2 rounded-lg">
            <span className="text-yellow-400 text-lg">🍚</span>
            <span className="text-yellow-300 font-bold">
              {character.gold} 밥
            </span>
          </div>

          {/* 스탯 */}
          <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
            <div className="text-red-400">STR {character.stats.strength}</div>
            <div className="text-green-400">AGI {character.stats.agility}</div>
            <div className="text-blue-400">VIT {character.stats.vitality}</div>
            <div className="text-purple-400">
              INT {character.stats.intelligence}
            </div>
          </div>

          {/* 장착 아이템 */}
          <div className="flex flex-col gap-1 text-xs text-white/80">
            {character.equipped.weapon && (
              <div>🐾 {character.equipped.weapon.name}</div>
            )}
            {character.equipped.armor && (
              <div>🧣 {character.equipped.armor.name}</div>
            )}
          </div>
        </div>

        {/* 우측: 온라인 정보 및 미니맵 */}
        <div className="flex items-center gap-6">
          {/* 온라인 상태 */}
          <div className="text-center">
            <div className="flex items-center gap-2 bg-green-600/20 px-3 py-2 rounded-lg mb-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-300 font-bold text-sm">
                {onlineCount} ONLINE
              </span>
            </div>
            {isGuest && (
              <span className="px-2 py-1 bg-amber-600/30 text-amber-300 text-xs rounded-md">
                게스트
              </span>
            )}
          </div>

          {/* 미니맵 */}
          <div className="relative">
            <canvas
              ref={minimapCanvasRef}
              className="rounded-lg border-2 border-white/20 shadow-lg bg-black/50"
              style={{ width: "100px", height: "100px" }}
            />
            <div className="absolute -bottom-5 left-1/2 transform -translate-x-1/2 text-xs text-white/60">
              미니맵
            </div>
          </div>

          {/* 위치 및 적 정보 */}
          <div className="text-xs text-white/60">
            <div>
              🗺️ ({Math.floor(character.pos.x / tileSize)},{" "}
              {Math.floor(character.pos.y / tileSize)})
            </div>
            <div>🐕 {enemyCount}마리</div>
          </div>
        </div>
      </div>
    </div>
  );
}
