"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Button } from "@/components/ui/button"

interface Vector2 {
  x: number
  y: number
}

interface Character {
  pos: Vector2
  vel: Vector2
  size: Vector2
  health: number
  maxHealth: number
  mana: number
  maxMana: number
  level: number
  exp: number
  expToNext: number
  stats: {
    strength: number
    agility: number
    vitality: number
  }
  state: "idle" | "running" | "attacking" | "casting" | "dodging"
  direction: number
  animFrame: number
  animTimer: number
  attackTimer: number
  dodgeTimer: number
  dodgeCooldown: number
  invulnerable: number
  weapon: "sword" | "bow" | "spear"
}

interface Enemy {
  id: number
  pos: Vector2
  vel: Vector2
  size: Vector2
  health: number
  maxHealth: number
  type: "bandit" | "wildBoar" | "tiger" | "ninja"
  state: "idle" | "chasing" | "attacking" | "hit" | "dying"
  direction: number
  animFrame: number
  animTimer: number
  hitTimer: number
  deathTimer: number
  knockback: Vector2
  attackCooldown: number
  expReward: number
}

interface Item {
  id: number
  pos: Vector2
  type: "coin" | "health" | "mana" | "weapon"
  value: number
  life: number
}

interface Particle {
  pos: Vector2
  vel: Vector2
  life: number
  maxLife: number
  color: string
  size: number
  type: "spark" | "blood" | "leaf" | "coin"
  rotation: number
  rotSpeed: number
}

interface Projectile {
  pos: Vector2
  vel: Vector2
  life: number
  damage: number
  type: "arrow" | "fireball"
}

export default function JoseonRPG() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const keysRef = useRef<Set<string>>(new Set())
  const lastTimeRef = useRef<number>(0)

  const [gameState, setGameState] = useState<"playing" | "paused" | "gameOver" | "levelUp">("playing")
  const [gold, setGold] = useState(0)
  const [killCount, setKillCount] = useState(0)

  // 화면 흔들림과 효과
  const screenShakeRef = useRef({ x: 0, y: 0, intensity: 0, duration: 0 })
  const timeStopRef = useRef({ duration: 0, intensity: 1 })

  const characterRef = useRef<Character>({
    pos: { x: 400, y: 300 },
    vel: { x: 0, y: 0 },
    size: { x: 36, y: 36 },
    health: 100,
    maxHealth: 100,
    mana: 50,
    maxMana: 50,
    level: 1,
    exp: 0,
    expToNext: 100,
    stats: {
      strength: 10,
      agility: 8,
      vitality: 12,
    },
    state: "idle",
    direction: 1,
    animFrame: 0,
    animTimer: 0,
    attackTimer: 0,
    dodgeTimer: 0,
    dodgeCooldown: 0,
    invulnerable: 0,
    weapon: "sword",
  })

  const enemiesRef = useRef<Enemy[]>([])
  const itemsRef = useRef<Item[]>([])
  const particlesRef = useRef<Particle[]>([])
  const projectilesRef = useRef<Projectile[]>([])

  // 전통 색상 팔레트
  const colors = {
    background: "#2d1810",
    ground: "#4a3728",
    character: "#8b4513",
    hanbok: "#dc143c",
    accent: "#ffd700",
    enemy: "#8b0000",
    ui: "#f4e4bc",
    text: "#2f1b14",
  }

  // 사운드 효과 (전통 악기 느낌)
  const playSound = useCallback(
    (frequency: number, duration: number, type: "sine" | "square" | "sawtooth" = "sine", volume = 0.1) => {
      try {
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        const oscillator = audioContext.createOscillator()
        const gainNode = audioContext.createGain()
        const filter = audioContext.createBiquadFilter()

        oscillator.connect(filter)
        filter.connect(gainNode)
        gainNode.connect(audioContext.destination)

        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime)
        oscillator.type = type

        filter.type = "lowpass"
        filter.frequency.setValueAtTime(frequency * 1.5, audioContext.currentTime)

        gainNode.gain.setValueAtTime(volume, audioContext.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration)

        oscillator.start(audioContext.currentTime)
        oscillator.stop(audioContext.currentTime + duration)
      } catch (e) {
        // 사운드 실패 시 무시
      }
    },
    [],
  )

  // 화면 흔들림
  const addScreenShake = useCallback((intensity: number, duration: number) => {
    screenShakeRef.current = { x: 0, y: 0, intensity, duration }
  }, [])

  // 시간 정지
  const addTimeStop = useCallback((duration: number, intensity = 0.1) => {
    timeStopRef.current = { duration, intensity }
  }, [])

  // 파티클 생성
  const createParticles = useCallback(
    (pos: Vector2, type: "spark" | "blood" | "leaf" | "coin", count = 8, color = "#ff4444") => {
      for (let i = 0; i < count; i++) {
        const angle = (Math.PI * 2 * i) / count + (Math.random() - 0.5) * 0.5
        const speed = 2 + Math.random() * 4

        particlesRef.current.push({
          pos: { x: pos.x, y: pos.y },
          vel: {
            x: Math.cos(angle) * speed,
            y: Math.sin(angle) * speed - (type === "leaf" ? 1 : 0),
          },
          life: type === "coin" ? 60 : 40,
          maxLife: type === "coin" ? 60 : 40,
          color,
          size: type === "coin" ? 6 : 4,
          type,
          rotation: angle,
          rotSpeed: (Math.random() - 0.5) * 0.2,
        })
      }
    },
    [],
  )

  // 아이템 드롭
  const dropItem = useCallback((pos: Vector2, type: "coin" | "health" | "mana" | "weapon", value = 1) => {
    itemsRef.current.push({
      id: Date.now() + Math.random(),
      pos: { x: pos.x, y: pos.y },
      type,
      value,
      life: 600, // 10초
    })
  }, [])

  // 적 스폰
  const spawnEnemy = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const types: Enemy["type"][] = ["bandit", "wildBoar", "tiger", "ninja"]
    const type = types[Math.floor(Math.random() * types.length)]

    const side = Math.floor(Math.random() * 4)
    let x, y

    switch (side) {
      case 0:
        x = Math.random() * canvas.width
        y = -50
        break
      case 1:
        x = canvas.width + 50
        y = Math.random() * canvas.height
        break
      case 2:
        x = Math.random() * canvas.width
        y = canvas.height + 50
        break
      default:
        x = -50
        y = Math.random() * canvas.height
        break
    }

    let health, expReward, size
    switch (type) {
      case "bandit":
        health = 3
        expReward = 15
        size = { x: 32, y: 32 }
        break
      case "wildBoar":
        health = 5
        expReward = 25
        size = { x: 40, y: 28 }
        break
      case "tiger":
        health = 8
        expReward = 40
        size = { x: 48, y: 32 }
        break
      case "ninja":
        health = 4
        expReward = 30
        size = { x: 30, y: 34 }
        break
    }

    enemiesRef.current.push({
      id: Date.now() + Math.random(),
      pos: { x, y },
      vel: { x: 0, y: 0 },
      size,
      health,
      maxHealth: health,
      type,
      state: "chasing",
      direction: 1,
      animFrame: 0,
      animTimer: 0,
      hitTimer: 0,
      deathTimer: 0,
      knockback: { x: 0, y: 0 },
      attackCooldown: 0,
      expReward,
    })
  }, [])

  // 레벨업 처리
  const levelUp = useCallback(() => {
    const character = characterRef.current
    character.level++
    character.exp = 0
    character.expToNext = character.level * 100
    character.stats.strength += 2
    character.stats.agility += 1
    character.stats.vitality += 3
    character.maxHealth = 80 + character.stats.vitality * 5
    character.maxMana = 30 + character.level * 10
    character.health = character.maxHealth
    character.mana = character.maxMana

    setGameState("levelUp")
    setTimeout(() => setGameState("playing"), 2000)

    playSound(523, 0.5, "sine", 0.2) // 도
    setTimeout(() => playSound(659, 0.5, "sine", 0.2), 200) // 미
    setTimeout(() => playSound(784, 0.5, "sine", 0.2), 400) // 솔
  }, [playSound])

  // 키보드 입력
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.key.toLowerCase())

      const character = characterRef.current

      if (e.key === " " || e.key === "Spacebar") {
        e.preventDefault()
        if (character.state !== "attacking" && character.attackTimer <= 0) {
          character.state = "attacking"
          character.attackTimer = character.weapon === "bow" ? 30 : 25

          if (character.weapon === "bow" && character.mana >= 10) {
            character.mana -= 10
            // 화살 발사
            projectilesRef.current.push({
              pos: { x: character.pos.x + character.size.x / 2, y: character.pos.y + character.size.y / 2 },
              vel: { x: character.direction * 8, y: 0 },
              life: 100,
              damage: character.stats.strength + 5,
              type: "arrow",
            })
            playSound(400, 0.2, "sawtooth", 0.15)
          } else {
            playSound(300, 0.15, "square", 0.15)
          }
        }
      }

      if (e.key === "shift" && character.dodgeCooldown <= 0) {
        character.state = "dodging"
        character.dodgeTimer = 20
        character.dodgeCooldown = 80
        character.invulnerable = 20
        playSound(250, 0.1, "sine", 0.1)
      }

      if (e.key === "q") {
        // 무기 변경
        const weapons: Character["weapon"][] = ["sword", "bow", "spear"]
        const currentIndex = weapons.indexOf(character.weapon)
        character.weapon = weapons[(currentIndex + 1) % weapons.length]
        playSound(200, 0.1, "square", 0.1)
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.key.toLowerCase())
    }

    window.addEventListener("keydown", handleKeyDown)
    window.addEventListener("keyup", handleKeyUp)

    return () => {
      window.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("keyup", handleKeyUp)
    }
  }, [playSound])

  // 충돌 감지
  const checkCollision = useCallback((a: any, b: any) => {
    return (
      a.pos.x < b.pos.x + b.size.x &&
      a.pos.x + a.size.x > b.pos.x &&
      a.pos.y < b.pos.y + b.size.y &&
      a.pos.y + a.size.y > b.pos.y
    )
  }, [])

  // 거리 계산
  const distance = useCallback((a: Vector2, b: Vector2) => {
    const dx = a.x - b.x
    const dy = a.y - b.y
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  // 게임 업데이트
  const updateGame = useCallback(
    (deltaTime: number) => {
      if (gameState !== "playing") return

      const canvas = canvasRef.current
      if (!canvas) return

      const timeScale = timeStopRef.current.duration > 0 ? timeStopRef.current.intensity : 1
      if (timeStopRef.current.duration > 0) {
        timeStopRef.current.duration--
      }

      const character = characterRef.current
      const keys = keysRef.current

      // 캐릭터 업데이트
      character.vel.x *= 0.85
      character.vel.y *= 0.85

      // 이동
      const speed = character.state === "dodging" ? 8 : 4 + character.stats.agility * 0.2
      let moving = false

      if (keys.has("a") || keys.has("arrowleft")) {
        character.vel.x = -speed
        character.direction = -1
        moving = true
      }
      if (keys.has("d") || keys.has("arrowright")) {
        character.vel.x = speed
        character.direction = 1
        moving = true
      }
      if (keys.has("w") || keys.has("arrowup")) {
        character.vel.y = -speed
        moving = true
      }
      if (keys.has("s") || keys.has("arrowdown")) {
        character.vel.y = speed
        moving = true
      }

      // 상태 업데이트
      if (character.attackTimer > 0) {
        character.attackTimer--
        if (character.attackTimer <= 0) {
          character.state = "idle"
        }
      }

      if (character.dodgeTimer > 0) {
        character.dodgeTimer--
        if (character.dodgeTimer <= 0) {
          character.state = "idle"
        }
      }

      if (character.dodgeCooldown > 0) character.dodgeCooldown--
      if (character.invulnerable > 0) character.invulnerable--

      // 마나 회복
      if (character.mana < character.maxMana) {
        character.mana += 0.1
      }

      if (character.state === "idle" && moving) {
        character.state = "running"
      } else if (character.state === "running" && !moving) {
        character.state = "idle"
      }

      // 위치 업데이트
      character.pos.x += character.vel.x * timeScale
      character.pos.y += character.vel.y * timeScale

      // 화면 경계
      character.pos.x = Math.max(0, Math.min(canvas.width - character.size.x, character.pos.x))
      character.pos.y = Math.max(0, Math.min(canvas.height - character.size.y, character.pos.y))

      // 애니메이션
      character.animTimer++
      if (character.animTimer > 8) {
        character.animFrame = (character.animFrame + 1) % 4
        character.animTimer = 0
      }

      // 투사체 업데이트
      projectilesRef.current = projectilesRef.current.filter((projectile) => {
        projectile.pos.x += projectile.vel.x * timeScale
        projectile.pos.y += projectile.vel.y * timeScale
        projectile.life--

        // 적과 충돌 체크
        enemiesRef.current.forEach((enemy, index) => {
          if (checkCollision(projectile, enemy) && enemy.state !== "dying") {
            enemy.health -= projectile.damage
            enemy.state = "hit"
            enemy.hitTimer = 15

            const knockbackForce = 10
            const dx = enemy.pos.x - projectile.pos.x
            const dy = enemy.pos.y - projectile.pos.y
            const dist = Math.sqrt(dx * dx + dy * dy)

            if (dist > 0) {
              enemy.knockback.x = (dx / dist) * knockbackForce
              enemy.knockback.y = (dy / dist) * knockbackForce
            }

            createParticles(
              { x: enemy.pos.x + enemy.size.x / 2, y: enemy.pos.y + enemy.size.y / 2 },
              "blood",
              8,
              "#8b0000",
            )

            addScreenShake(4, 10)
            playSound(200, 0.15, "sawtooth", 0.15)

            if (enemy.health <= 0) {
              enemy.state = "dying"
              enemy.deathTimer = 0
              character.exp += enemy.expReward
              setKillCount((prev) => prev + 1)

              // 아이템 드롭
              if (Math.random() < 0.7) dropItem(enemy.pos, "coin", 5 + Math.floor(Math.random() * 10))
              if (Math.random() < 0.3) dropItem(enemy.pos, "health", 20)

              createParticles(
                { x: enemy.pos.x + enemy.size.x / 2, y: enemy.pos.y + enemy.size.y / 2 },
                "coin",
                12,
                colors.accent,
              )

              addScreenShake(8, 20)
              playSound(350, 0.3, "sine", 0.2)
            }

            projectile.life = 0
          }
        })

        return projectile.life > 0 && projectile.pos.x > -50 && projectile.pos.x < canvas.width + 50
      })

      // 적 업데이트
      enemiesRef.current.forEach((enemy, index) => {
        if (enemy.state === "dying") {
          enemy.deathTimer++
          if (enemy.deathTimer > 40) {
            enemiesRef.current.splice(index, 1)
          }
          return
        }

        // 넉백 처리
        if (enemy.knockback.x !== 0 || enemy.knockback.y !== 0) {
          enemy.pos.x += enemy.knockback.x * timeScale
          enemy.pos.y += enemy.knockback.y * timeScale
          enemy.knockback.x *= 0.9
          enemy.knockback.y *= 0.9
          if (Math.abs(enemy.knockback.x) < 0.1) enemy.knockback.x = 0
          if (Math.abs(enemy.knockback.y) < 0.1) enemy.knockback.y = 0
        } else if (enemy.state === "chasing") {
          const dist = distance(enemy.pos, character.pos)
          if (dist > 0) {
            const dx = (character.pos.x - enemy.pos.x) / dist
            const dy = (character.pos.y - enemy.pos.y) / dist
            let speed = 1.5

            switch (enemy.type) {
              case "tiger":
                speed = 2.2
                break
              case "ninja":
                speed = 2.8
                break
              case "wildBoar":
                speed = 1.8
                break
            }

            enemy.vel.x = dx * speed
            enemy.vel.y = dy * speed
            enemy.direction = dx > 0 ? 1 : -1
          }
        }

        enemy.pos.x += enemy.vel.x * timeScale
        enemy.pos.y += enemy.vel.y * timeScale

        if (enemy.state === "hit") {
          enemy.hitTimer--
          if (enemy.hitTimer <= 0) {
            enemy.state = "chasing"
          }
        }

        // 캐릭터와 충돌
        if (checkCollision(character, enemy) && character.invulnerable <= 0) {
          let damage = 8
          switch (enemy.type) {
            case "tiger":
              damage = 15
              break
            case "ninja":
              damage = 12
              break
            case "wildBoar":
              damage = 10
              break
          }

          character.health -= damage
          character.invulnerable = 60
          addScreenShake(10, 25)
          playSound(150, 0.3, "square", 0.25)

          if (character.health <= 0) {
            setGameState("gameOver")
          }
        }

        // 근접 공격 범위
        if (character.state === "attacking" && character.attackTimer > 15 && character.weapon !== "bow") {
          const attackRange = {
            pos: {
              x: character.pos.x + (character.direction > 0 ? character.size.x : -50),
              y: character.pos.y - 10,
            },
            size: { x: 60, y: character.size.y + 20 },
          }

          if (checkCollision(attackRange, enemy) && enemy.state !== "hit" && enemy.state !== "dying") {
            let damage = character.stats.strength + 5
            if (character.weapon === "spear") damage += 3

            enemy.health -= damage
            enemy.state = "hit"
            enemy.hitTimer = 15

            const knockbackForce = character.weapon === "spear" ? 20 : 15
            const dx = enemy.pos.x - character.pos.x
            const dy = enemy.pos.y - character.pos.y
            const dist = Math.sqrt(dx * dx + dy * dy)

            if (dist > 0) {
              enemy.knockback.x = (dx / dist) * knockbackForce
              enemy.knockback.y = (dy / dist) * knockbackForce
            }

            createParticles(
              { x: enemy.pos.x + enemy.size.x / 2, y: enemy.pos.y + enemy.size.y / 2 },
              "blood",
              10,
              "#8b0000",
            )

            addScreenShake(6, 15)
            addTimeStop(8, 0.3)
            playSound(250, 0.2, "sawtooth", 0.2)

            if (enemy.health <= 0) {
              enemy.state = "dying"
              enemy.deathTimer = 0
              character.exp += enemy.expReward
              setKillCount((prev) => prev + 1)

              // 아이템 드롭
              if (Math.random() < 0.8) dropItem(enemy.pos, "coin", 8 + Math.floor(Math.random() * 15))
              if (Math.random() < 0.4) dropItem(enemy.pos, "health", 25)

              createParticles(
                { x: enemy.pos.x + enemy.size.x / 2, y: enemy.pos.y + enemy.size.y / 2 },
                "coin",
                15,
                colors.accent,
              )

              addScreenShake(10, 25)
              addTimeStop(12, 0.2)
              playSound(400, 0.3, "sine", 0.25)
            }
          }
        }

        enemy.animTimer++
        if (enemy.animTimer > 12) {
          enemy.animFrame = (enemy.animFrame + 1) % 3
          enemy.animTimer = 0
        }
      })

      // 아이템 업데이트
      itemsRef.current = itemsRef.current.filter((item) => {
        item.life--

        if (checkCollision(character, item)) {
          switch (item.type) {
            case "coin":
              setGold((prev) => prev + item.value)
              createParticles(item.pos, "coin", 6, colors.accent)
              playSound(400, 0.1, "sine", 0.15)
              break
            case "health":
              character.health = Math.min(character.maxHealth, character.health + item.value)
              createParticles(item.pos, "leaf", 8, "#90EE90")
              playSound(300, 0.2, "sine", 0.15)
              break
            case "mana":
              character.mana = Math.min(character.maxMana, character.mana + item.value)
              createParticles(item.pos, "spark", 8, "#4169E1")
              playSound(350, 0.2, "sine", 0.15)
              break
          }
          return false
        }

        return item.life > 0
      })

      // 레벨업 체크
      if (character.exp >= character.expToNext) {
        levelUp()
      }

      // 파티클 업데이트
      particlesRef.current = particlesRef.current.filter((particle) => {
        particle.pos.x += particle.vel.x * timeScale
        particle.pos.y += particle.vel.y * timeScale
        particle.vel.x *= 0.95
        particle.vel.y *= 0.95
        if (particle.type === "leaf") particle.vel.y += 0.1
        particle.rotation += particle.rotSpeed
        particle.life--
        return particle.life > 0
      })

      // 화면 흔들림
      if (screenShakeRef.current.duration > 0) {
        screenShakeRef.current.x = (Math.random() - 0.5) * screenShakeRef.current.intensity
        screenShakeRef.current.y = (Math.random() - 0.5) * screenShakeRef.current.intensity
        screenShakeRef.current.duration--
      } else {
        screenShakeRef.current.x = 0
        screenShakeRef.current.y = 0
      }

      // 적 스폰
      if (Math.random() < 0.012 + character.level * 0.002) {
        spawnEnemy()
      }
    },
    [
      gameState,
      checkCollision,
      distance,
      createParticles,
      dropItem,
      addScreenShake,
      addTimeStop,
      playSound,
      spawnEnemy,
      levelUp,
    ],
  )

  // 렌더링
  const render = useCallback(() => {
    const canvas = canvasRef.current
    const ctx = canvas?.getContext("2d")
    if (!canvas || !ctx) return

    ctx.save()
    ctx.translate(screenShakeRef.current.x, screenShakeRef.current.y)

    // 배경 (조선시대 느낌)
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
    gradient.addColorStop(0, "#4a3728")
    gradient.addColorStop(0.7, "#2d1810")
    gradient.addColorStop(1, "#1a0f08")
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 전통 패턴 배경
    ctx.strokeStyle = "rgba(139, 69, 19, 0.1)"
    ctx.lineWidth = 2
    for (let x = 0; x < canvas.width; x += 80) {
      for (let y = 0; y < canvas.height; y += 80) {
        ctx.beginPath()
        ctx.arc(x, y, 20, 0, Math.PI * 2)
        ctx.stroke()
      }
    }

    const character = characterRef.current

    // 투사체 렌더링
    projectilesRef.current.forEach((projectile) => {
      if (projectile.type === "arrow") {
        ctx.fillStyle = "#8B4513"
        ctx.fillRect(projectile.pos.x - 8, projectile.pos.y - 2, 16, 4)
        ctx.fillStyle = "#FFD700"
        ctx.fillRect(projectile.pos.x - 10, projectile.pos.y - 1, 4, 2)
      }
    })

    // 캐릭터 렌더링
    ctx.save()
    ctx.translate(character.pos.x + character.size.x / 2, character.pos.y + character.size.y / 2)
    if (character.direction < 0) ctx.scale(-1, 1)

    // 캐릭터 색상 (무적 시 깜빡임)
    let characterColor = colors.hanbok
    if (character.invulnerable > 0 && Math.floor(character.invulnerable / 5) % 2) {
      characterColor = "rgba(220, 20, 60, 0.5)"
    }
    if (character.state === "attacking") {
      characterColor = colors.accent
    }

    // 한복 몸체
    ctx.fillStyle = characterColor
    ctx.fillRect(-character.size.x / 2, -character.size.y / 2, character.size.x, character.size.y)

    // 갓 (전통 모자)
    ctx.fillStyle = "#2F1B14"
    ctx.fillRect(-character.size.x / 2 - 4, -character.size.y / 2 - 8, character.size.x + 8, 6)

    // 얼굴
    ctx.fillStyle = "#FDBCB4"
    ctx.fillRect(-8, -12, 16, 12)

    // 눈
    ctx.fillStyle = "#000000"
    ctx.fillRect(2, -8, 3, 2)

    // 무기 렌더링
    if (character.state === "attacking") {
      ctx.strokeStyle = "#C0C0C0"
      ctx.lineWidth = 4
      ctx.lineCap = "round"

      switch (character.weapon) {
        case "sword":
          ctx.beginPath()
          ctx.moveTo(20, -5)
          ctx.lineTo(35, -10)
          ctx.stroke()
          // 검 손잡이
          ctx.strokeStyle = "#8B4513"
          ctx.lineWidth = 6
          ctx.beginPath()
          ctx.moveTo(18, -3)
          ctx.lineTo(22, -7)
          ctx.stroke()
          break
        case "spear":
          ctx.beginPath()
          ctx.moveTo(25, 0)
          ctx.lineTo(45, -5)
          ctx.stroke()
          // 창날
          ctx.fillStyle = "#C0C0C0"
          ctx.fillRect(43, -8, 8, 6)
          break
        case "bow":
          // 활
          ctx.strokeStyle = "#8B4513"
          ctx.lineWidth = 3
          ctx.beginPath()
          ctx.arc(25, 0, 15, -Math.PI / 2, Math.PI / 2)
          ctx.stroke()
          break
      }
    }

    ctx.restore()

    // 적들 렌더링
    enemiesRef.current.forEach((enemy) => {
      ctx.save()
      ctx.translate(enemy.pos.x + enemy.size.x / 2, enemy.pos.y + enemy.size.y / 2)
      if (enemy.direction < 0) ctx.scale(-1, 1)

      let enemyColor = colors.enemy
      if (enemy.state === "hit") enemyColor = "#FFFFFF"
      if (enemy.state === "dying") {
        const alpha = 1 - enemy.deathTimer / 40
        enemyColor = `rgba(139, 0, 0, ${alpha})`
      }

      // 적 타입별 렌더링
      switch (enemy.type) {
        case "bandit":
          ctx.fillStyle = enemyColor
          ctx.fillRect(-enemy.size.x / 2, -enemy.size.y / 2, enemy.size.x, enemy.size.y)
          // 도적 두건
          ctx.fillStyle = "#2F1B14"
          ctx.fillRect(-enemy.size.x / 2, -enemy.size.y / 2 - 4, enemy.size.x, 8)
          break
        case "wildBoar":
          ctx.fillStyle = "#8B4513"
          ctx.fillRect(-enemy.size.x / 2, -enemy.size.y / 2, enemy.size.x, enemy.size.y)
          // 멧돼지 엄니
          ctx.fillStyle = "#FFFFFF"
          ctx.fillRect(enemy.size.x / 2 - 8, -2, 6, 3)
          ctx.fillRect(enemy.size.x / 2 - 8, 2, 6, 3)
          break
        case "tiger":
          ctx.fillStyle = "#FF8C00"
          ctx.fillRect(-enemy.size.x / 2, -enemy.size.y / 2, enemy.size.x, enemy.size.y)
          // 호랑이 줄무늬
          ctx.fillStyle = "#000000"
          for (let i = 0; i < 3; i++) {
            ctx.fillRect(-enemy.size.x / 2 + i * 12, -enemy.size.y / 2, 4, enemy.size.y)
          }
          break
        case "ninja":
          ctx.fillStyle = "#2F2F2F"
          ctx.fillRect(-enemy.size.x / 2, -enemy.size.y / 2, enemy.size.x, enemy.size.y)
          // 닌자 눈
          ctx.fillStyle = "#FF0000"
          ctx.fillRect(4, -6, 4, 2)
          break
      }

      ctx.restore()

      // 체력바
      if (enemy.health < enemy.maxHealth && enemy.state !== "dying") {
        ctx.fillStyle = "rgba(0, 0, 0, 0.7)"
        ctx.fillRect(enemy.pos.x, enemy.pos.y - 10, enemy.size.x, 6)
        ctx.fillStyle = colors.enemy
        ctx.fillRect(enemy.pos.x, enemy.pos.y - 10, (enemy.health / enemy.maxHealth) * enemy.size.x, 6)
      }
    })

    // 아이템 렌더링
    itemsRef.current.forEach((item) => {
      ctx.save()
      ctx.translate(item.pos.x, item.pos.y)

      switch (item.type) {
        case "coin":
          ctx.fillStyle = colors.accent
          ctx.beginPath()
          ctx.arc(0, 0, 8, 0, Math.PI * 2)
          ctx.fill()
          ctx.fillStyle = "#B8860B"
          ctx.beginPath()
          ctx.arc(0, 0, 5, 0, Math.PI * 2)
          ctx.fill()
          break
        case "health":
          ctx.fillStyle = "#90EE90"
          ctx.fillRect(-6, -6, 12, 12)
          ctx.fillStyle = "#228B22"
          ctx.fillRect(-2, -8, 4, 16)
          ctx.fillRect(-8, -2, 16, 4)
          break
        case "mana":
          ctx.fillStyle = "#4169E1"
          ctx.beginPath()
          ctx.arc(0, 0, 6, 0, Math.PI * 2)
          ctx.fill()
          break
      }

      ctx.restore()
    })

    // 파티클 렌더링
    particlesRef.current.forEach((particle) => {
      const alpha = particle.life / particle.maxLife
      ctx.save()
      ctx.translate(particle.pos.x, particle.pos.y)
      ctx.rotate(particle.rotation)

      switch (particle.type) {
        case "coin":
          ctx.fillStyle =
            particle.color +
            Math.floor(alpha * 255)
              .toString(16)
              .padStart(2, "0")
          ctx.beginPath()
          ctx.arc(0, 0, particle.size, 0, Math.PI * 2)
          ctx.fill()
          break
        case "leaf":
          ctx.fillStyle =
            particle.color +
            Math.floor(alpha * 255)
              .toString(16)
              .padStart(2, "0")
          ctx.beginPath()
          ctx.ellipse(0, 0, particle.size, particle.size * 1.5, 0, 0, Math.PI * 2)
          ctx.fill()
          break
        default:
          ctx.fillStyle =
            particle.color +
            Math.floor(alpha * 255)
              .toString(16)
              .padStart(2, "0")
          ctx.fillRect(-particle.size / 2, -particle.size / 2, particle.size, particle.size)
          break
      }

      ctx.restore()
    })

    ctx.restore()

    // UI 렌더링 (전통 스타일)
    // 배경 패널
    ctx.fillStyle = "rgba(244, 228, 188, 0.9)"
    ctx.fillRect(10, 10, 300, 120)
    ctx.strokeStyle = colors.text
    ctx.lineWidth = 2
    ctx.strokeRect(10, 10, 300, 120)

    // 텍스트
    ctx.fillStyle = colors.text
    ctx.font = "bold 18px serif"
    ctx.fillText(`레벨 ${character.level} 무사`, 20, 35)

    ctx.font = "14px serif"
    ctx.fillText(`체력: ${Math.max(0, Math.floor(character.health))}/${character.maxHealth}`, 20, 55)
    ctx.fillText(`기력: ${Math.floor(character.mana)}/${character.maxMana}`, 20, 75)
    ctx.fillText(`경험치: ${character.exp}/${character.expToNext}`, 20, 95)
    ctx.fillText(`황금: ${gold}냥`, 20, 115)

    // 스탯 표시
    ctx.fillStyle = "rgba(244, 228, 188, 0.9)"
    ctx.fillRect(canvas.width - 200, 10, 180, 100)
    ctx.strokeRect(canvas.width - 200, 10, 180, 100)

    ctx.fillStyle = colors.text
    ctx.font = "bold 16px serif"
    ctx.fillText("능력치", canvas.width - 190, 30)
    ctx.font = "12px serif"
    ctx.fillText(`힘: ${character.stats.strength}`, canvas.width - 190, 50)
    ctx.fillText(`민첩: ${character.stats.agility}`, canvas.width - 190, 70)
    ctx.fillText(`체력: ${character.stats.vitality}`, canvas.width - 190, 90)

    // 무기 표시
    ctx.fillText(
      `무기: ${character.weapon === "sword" ? "검" : character.weapon === "bow" ? "활" : "창"}`,
      canvas.width - 100,
      50,
    )
    ctx.fillText(`처치: ${killCount}`, canvas.width - 100, 70)

    // 쿨다운 표시
    if (character.dodgeCooldown > 0) {
      ctx.fillStyle = "rgba(244, 228, 188, 0.7)"
      ctx.fillRect(canvas.width - 150, canvas.height - 40, 120, 15)
      ctx.fillStyle = colors.hanbok
      ctx.fillRect(canvas.width - 150, canvas.height - 40, (1 - character.dodgeCooldown / 80) * 120, 15)
      ctx.fillStyle = colors.text
      ctx.font = "12px serif"
      ctx.fillText("회피 준비중", canvas.width - 145, canvas.height - 28)
    }

    // 조작법
    ctx.fillStyle = "rgba(244, 228, 188, 0.8)"
    ctx.font = "12px serif"
    ctx.fillText("WASD: 이동 | 스페이스: 공격 | Shift: 회피 | Q: 무기변경", 20, canvas.height - 10)

    // 레벨업 화면
    if (gameState === "levelUp") {
      ctx.fillStyle = "rgba(220, 20, 60, 0.8)"
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      ctx.fillStyle = colors.accent
      ctx.font = "bold 48px serif"
      ctx.textAlign = "center"
      ctx.fillText("레벨 상승!", canvas.width / 2, canvas.height / 2 - 50)
      ctx.font = "24px serif"
      ctx.fillText(`${character.level}급 무사가 되었습니다!`, canvas.width / 2, canvas.height / 2)
      ctx.fillText("능력이 향상되었습니다!", canvas.width / 2, canvas.height / 2 + 30)
      ctx.textAlign = "left"
    }

    // 게임 오버
    if (gameState === "gameOver") {
      ctx.fillStyle = "rgba(0, 0, 0, 0.9)"
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      ctx.fillStyle = colors.accent
      ctx.font = "bold 48px serif"
      ctx.textAlign = "center"
      ctx.fillText("전사!", canvas.width / 2, canvas.height / 2 - 50)
      ctx.font = "24px serif"
      ctx.fillText(`${character.level}급까지 도달`, canvas.width / 2, canvas.height / 2)
      ctx.fillText(`${killCount}명의 적을 처치`, canvas.width / 2, canvas.height / 2 + 30)
      ctx.fillText(`${gold}냥의 황금 획득`, canvas.width / 2, canvas.height / 2 + 60)
      ctx.textAlign = "left"
    }
  }, [gameState, gold, killCount, colors])

  // 게임 루프
  useEffect(() => {
    const gameLoop = (currentTime: number) => {
      const deltaTime = currentTime - lastTimeRef.current
      lastTimeRef.current = currentTime

      updateGame(deltaTime)
      render()

      animationRef.current = requestAnimationFrame(gameLoop)
    }

    animationRef.current = requestAnimationFrame(gameLoop)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [updateGame, render])

  const resetGame = () => {
    characterRef.current = {
      pos: { x: 400, y: 300 },
      vel: { x: 0, y: 0 },
      size: { x: 36, y: 36 },
      health: 100,
      maxHealth: 100,
      mana: 50,
      maxMana: 50,
      level: 1,
      exp: 0,
      expToNext: 100,
      stats: {
        strength: 10,
        agility: 8,
        vitality: 12,
      },
      state: "idle",
      direction: 1,
      animFrame: 0,
      animTimer: 0,
      attackTimer: 0,
      dodgeTimer: 0,
      dodgeCooldown: 0,
      invulnerable: 0,
      weapon: "sword",
    }
    enemiesRef.current = []
    itemsRef.current = []
    particlesRef.current = []
    projectilesRef.current = []
    setGold(0)
    setKillCount(0)
    setGameState("playing")
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-amber-50 p-4">
      <div className="mb-4">
        <h1 className="text-5xl font-bold text-amber-900 mb-2 text-center">🏯 조선무사전 ⚔️</h1>
        <p className="text-lg text-amber-800 text-center mb-4">조선시대 무사가 되어 적들을 물리치세요!</p>
        <div className="flex gap-4 justify-center">
          <Button onClick={resetGame} className="bg-red-800 hover:bg-red-900 text-white font-bold px-6 py-2">
            새로운 무사
          </Button>
        </div>
      </div>

      <canvas
        ref={canvasRef}
        width={800}
        height={600}
        className="border-4 border-amber-800 rounded-lg shadow-2xl"
        style={{ background: "linear-gradient(to bottom, #8B4513, #2F1B14)" }}
        tabIndex={0}
      />

      <div className="mt-4 text-center text-amber-800 max-w-2xl">
        <p className="text-lg font-semibold">🎮 WASD: 이동 | 스페이스: 공격 | Shift: 회피 | Q: 무기 변경 (검/활/창)</p>
        <p className="text-sm mt-2">
          적을 처치하여 경험치와 황금을 얻고, 레벨을 올려 더 강한 무사가 되세요!
          <br />
          도적, 멧돼지, 호랑이, 닌자 등 다양한 적들이 기다리고 있습니다.
        </p>
      </div>
    </div>
  )
}
