rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // 인증된 사용자만 자신의 데이터에 접근 가능
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // 모든 인증된 사용자가 온라인 플레이어 정보 읽기/쓰기 가능
    match /onlinePlayers/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // 모든 인증된 사용자가 채팅 읽기/쓰기 가능
    match /globalChat/{messageId} {
      allow read, write: if request.auth != null;
    }
  }
}