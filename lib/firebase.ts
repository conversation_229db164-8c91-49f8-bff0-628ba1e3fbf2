// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB5oonVqbBEgdNlEaO6NFNNSkn-AxH-_KM",
  authDomain: "the-project7039.firebaseapp.com",
  projectId: "the-project7039",
  storageBucket: "the-project7039.firebasestorage.app",
  messagingSenderId: "1029948729390",
  appId: "1:1029948729390:web:a6793f872560a3934a57bf",
  measurementId: "G-8554JDLFR2",
};

// Initialize Firebase
console.log("🔥 Initializing Firebase with config:", firebaseConfig);
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);

console.log("✅ Firebase initialized successfully");
console.log("🔥 Firestore instance:", db);
console.log("🔐 Auth instance:", auth);

// Analytics removed to prevent SSR issues

export default app;
