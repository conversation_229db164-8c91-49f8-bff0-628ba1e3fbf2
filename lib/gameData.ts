import { User } from "firebase/auth";
import {
  addDoc,
  collection,
  doc,
  getDoc,
  limit,
  onSnapshot,
  orderBy,
  query,
  serverTimestamp,
  setDoc,
  Timestamp,
  updateDoc,
} from "firebase/firestore";
import { db } from "./firebase";

export interface InventoryItem {
  id: string;
  name: string;
  type: "weapon" | "armor" | "potion" | "misc";
  quantity: number;
  description: string;
  icon?: string;
  rarity?: "common" | "uncommon" | "rare" | "epic" | "legendary";
  stats?: {
    attack?: number;
    defense?: number;
    health?: number;
    mana?: number;
  };
}

export interface EquippedItems {
  weapon?: InventoryItem;
  armor?: InventoryItem;
  accessory?: InventoryItem;
}

export interface GameData {
  level: number;
  exp: number;
  expToNext: number;
  health: number;
  maxHealth: number;
  mana: number;
  maxMana: number;
  gold: number;
  stats: {
    strength: number;
    agility: number;
    vitality: number;
    intelligence: number;
  };
  inventory: InventoryItem[];
  equipped: EquippedItems;
  position: { x: number; y: number };
  lastSaved: Date;
}

export interface UserProfile {
  email: string;
  displayName: string;
  createdAt: Date;
  gameData: GameData;
}

export interface ChatMessage {
  id?: string;
  userId: string;
  displayName: string;
  message: string;
  timestamp: Timestamp | Date;
  characterLevel?: number;
}

export interface OnlinePlayer {
  id?: string;
  userId: string;
  displayName: string;
  level: number;
  position: { x: number; y: number };
  direction: number;
  state: "idle" | "walking" | "attacking" | "talking";
  lastActive: Timestamp | Date;
  health: number;
  maxHealth: number;
}

// 게임 데이터 로드
export const loadGameData = async (user: User): Promise<GameData | null> => {
  try {
    const userDoc = await getDoc(doc(db, "users", user.uid));

    if (userDoc.exists()) {
      const data = userDoc.data() as UserProfile;
      return data.gameData;
    }

    return null;
  } catch (error) {
    console.error("Error loading game data:", error);
    return null;
  }
};

// 게임 데이터 저장
export const saveGameData = async (
  user: User,
  gameData: Partial<GameData>
): Promise<boolean> => {
  try {
    const userDocRef = doc(db, "users", user.uid);
    const updateData: any = {};

    // gameData의 각 필드를 Firestore 형식으로 변환
    Object.entries(gameData).forEach(([key, value]) => {
      updateData[`gameData.${key}`] = value;
    });

    // 마지막 저장 시간 업데이트
    updateData["gameData.lastSaved"] = new Date();

    await updateDoc(userDocRef, updateData);
    return true;
  } catch (error) {
    console.error("Error saving game data:", error);
    return false;
  }
};

// 전체 게임 데이터 저장 (전체 덮어쓰기)
export const saveFullGameData = async (
  user: User,
  gameData: GameData
): Promise<boolean> => {
  try {
    const userDocRef = doc(db, "users", user.uid);

    await updateDoc(userDocRef, {
      gameData: {
        ...gameData,
        lastSaved: new Date(),
      },
    });

    return true;
  } catch (error) {
    console.error("Error saving full game data:", error);
    return false;
  }
};

// 사용자 프로필 정보 가져오기
export const getUserProfile = async (
  user: User
): Promise<UserProfile | null> => {
  try {
    const userDoc = await getDoc(doc(db, "users", user.uid));

    if (userDoc.exists()) {
      return userDoc.data() as UserProfile;
    }

    return null;
  } catch (error) {
    console.error("Error getting user profile:", error);
    return null;
  }
};

// 게임 순위표용 데이터 (레벨 기준)
export const updatePlayerRanking = async (
  user: User,
  level: number,
  displayName: string
): Promise<boolean> => {
  try {
    const rankingDocRef = doc(db, "rankings", user.uid);

    await setDoc(rankingDocRef, {
      uid: user.uid,
      displayName: displayName,
      level: level,
      updatedAt: new Date(),
    });

    return true;
  } catch (error) {
    console.error("Error updating player ranking:", error);
    return false;
  }
};

// 자동 저장 간격 (밀리초)
export const AUTO_SAVE_INTERVAL = 30000; // 30초마다 자동 저장

// === 채팅 시스템 ===

// 채팅 메시지 전송
export const sendChatMessage = async (
  user: User,
  message: string,
  displayName: string,
  characterLevel?: number
): Promise<boolean> => {
  try {
    console.log("🔥 Attempting to send chat message:", {
      userId: user.uid,
      displayName,
      message: message.trim(),
    });

    const chatCollection = collection(db, "globalChat");

    await addDoc(chatCollection, {
      userId: user.uid,
      displayName: displayName,
      message: message.trim(),
      timestamp: serverTimestamp(),
      characterLevel: characterLevel || 1,
    });

    console.log("✅ Chat message sent successfully!");
    return true;
  } catch (error: any) {
    console.error("❌ FIRESTORE ERROR - Chat message failed:", error);
    console.error("Error code:", error.code);
    console.error("Error message:", error.message);
    return false;
  }
};

// 채팅 메시지 실시간 구독
export const subscribeToChatMessages = (
  callback: (messages: ChatMessage[]) => void,
  messageLimit: number = 50
) => {
  const chatCollection = collection(db, "globalChat");
  const chatQuery = query(
    chatCollection,
    orderBy("timestamp", "desc"),
    limit(messageLimit)
  );

  return onSnapshot(
    chatQuery,
    (snapshot) => {
      try {
        const messages: ChatMessage[] = [];

        snapshot.forEach((doc) => {
          const data = doc.data();
          if (data && data.userId && data.message) {
            messages.push({
              id: doc.id,
              userId: data.userId,
              displayName: data.displayName || "무명의 무사",
              message: data.message,
              timestamp: data.timestamp,
              characterLevel: data.characterLevel || 1,
            });
          }
        });

        // 메시지를 시간순으로 정렬 (최신 메시지가 아래로)
        messages.reverse();
        callback(messages);
      } catch (error) {
        console.error("Error processing chat messages snapshot:", error);
        callback([]);
      }
    },
    (error) => {
      console.error("Error in chat messages subscription:", error);
      callback([]);
    }
  );
};

// === 멀티플레이어 시스템 ===

// 플레이어 위치 업데이트
export const updatePlayerPosition = async (
  user: User,
  displayName: string,
  level: number,
  position: { x: number; y: number },
  direction: number,
  state: "idle" | "walking" | "attacking" | "talking",
  health: number,
  maxHealth: number
): Promise<boolean> => {
  try {
    if (!user || !user.uid) {
      console.warn("Cannot update position: user not authenticated");
      return false;
    }

    const playerDocRef = doc(db, "onlinePlayers", user.uid);

    // 데이터 유효성 검사
    const updateData = {
      userId: user.uid,
      displayName: displayName || "무명의 무사",
      level: Math.max(1, level || 1),
      position: {
        x: Math.round(position?.x || 0),
        y: Math.round(position?.y || 0),
      },
      direction: direction || 1,
      state: state || "idle",
      lastActive: serverTimestamp(),
      health: Math.max(0, health || 100),
      maxHealth: Math.max(1, maxHealth || 100),
    };

    await setDoc(playerDocRef, updateData, { merge: true });
    console.log("✅ Player position updated successfully:", updateData);
    return true;
  } catch (error: any) {
    console.error("❌ FIRESTORE ERROR - Player position update failed:", error);
    console.error("Error code:", error.code);
    console.error("Error message:", error.message);
    return false;
  }
};

// 온라인 플레이어들 실시간 구독
export const subscribeToOnlinePlayers = (
  currentUserId: string,
  callback: (players: OnlinePlayer[]) => void
) => {
  const playersCollection = collection(db, "onlinePlayers");

  return onSnapshot(
    playersCollection,
    (snapshot) => {
      try {
        const players: OnlinePlayer[] = [];

        snapshot.forEach((doc) => {
          const data = doc.data();
          console.log("Found player document:", doc.id, data);

          // 자신은 제외하고 최근 30초 이내 활동한 플레이어들만 포함
          if (data.userId !== currentUserId) {
            const lastActive = data.lastActive?.toDate
              ? data.lastActive.toDate()
              : new Date(data.lastActive);
            const now = new Date();
            const timeDiff = now.getTime() - lastActive.getTime();

            console.log(
              `Player ${data.displayName}: timeDiff=${timeDiff}ms, userId=${data.userId}, currentUserId=${currentUserId}`
            );

            // 60초 이내 활동한 플레이어만 표시 (30초에서 60초로 증가)
            if (timeDiff <= 60000) {
              const player = {
                id: doc.id,
                userId: data.userId,
                displayName: data.displayName || "무명의 무사",
                level: data.level || 1,
                position: data.position || { x: 0, y: 0 },
                direction: data.direction || 1,
                state: data.state || "idle",
                lastActive: data.lastActive,
                health: data.health || 100,
                maxHealth: data.maxHealth || 100,
              };
              console.log("Adding player to list:", player);
              players.push(player);
            } else {
              console.log(
                `Player ${data.displayName} excluded (inactive for ${timeDiff}ms)`
              );
            }
          } else {
            console.log("Skipping own player:", data.userId);
          }
        });

        callback(players);
      } catch (error) {
        console.error("Error processing online players snapshot:", error);
        callback([]);
      }
    },
    (error) => {
      console.error("Error in online players subscription:", error);
      callback([]);
    }
  );
};

// 플레이어 접속 종료 시 정리
export const removePlayerFromOnline = async (user: User): Promise<boolean> => {
  try {
    if (!user || !user.uid) {
      console.warn("Cannot remove player: user not authenticated");
      return false;
    }

    const playerDocRef = doc(db, "onlinePlayers", user.uid);

    // 문서 존재 확인 후 업데이트
    const docSnap = await getDoc(playerDocRef);
    if (docSnap.exists()) {
      await updateDoc(playerDocRef, {
        lastActive: new Date(Date.now() - 60000), // 1분 전으로 설정해서 곧 사라지게 함
      });
    }

    return true;
  } catch (error) {
    console.error("Error removing player from online:", error);
    return false;
  }
};
