"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Button } from "@/components/ui/button"

interface Position {
  x: number
  y: number
}

interface Character {
  x: number
  y: number
  width: number
  height: number
  health: number
  maxHealth: number
  isAttacking: boolean
  attackCooldown: number
  direction: number // 0: right, 1: left
}

interface Enemy {
  id: number
  x: number
  y: number
  width: number
  height: number
  health: number
  maxHealth: number
  speed: number
  isHit: boolean
  hitTimer: number
  knockbackX: number
  knockbackY: number
}

interface Particle {
  x: number
  y: number
  vx: number
  vy: number
  life: number
  maxLife: number
  color: string
  size: number
}

export default function Component() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const keysRef = useRef<Set<string>>(new Set())

  const [gameState, setGameState] = useState<"playing" | "paused" | "gameOver">("playing")
  const [score, setScore] = useState(0)
  const [wave, setWave] = useState(1)

  const characterRef = useRef<Character>({
    x: 400,
    y: 300,
    width: 40,
    height: 40,
    health: 100,
    maxHealth: 100,
    isAttacking: false,
    attackCooldown: 0,
    direction: 0,
  })

  const enemiesRef = useRef<Enemy[]>([])
  const particlesRef = useRef<Particle[]>([])
  const lastTimeRef = useRef<number>(0)

  // 사운드 효과
  const playSound = useCallback(
    (frequency: number, duration: number, type: "sine" | "square" | "sawtooth" = "sine") => {
      try {
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        const oscillator = audioContext.createOscillator()
        const gainNode = audioContext.createGain()

        oscillator.connect(gainNode)
        gainNode.connect(audioContext.destination)

        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime)
        oscillator.type = type

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration)

        oscillator.start(audioContext.currentTime)
        oscillator.stop(audioContext.currentTime + duration)
      } catch (e) {
        // 사운드 재생 실패 시 무시
      }
    },
    [],
  )

  // 파티클 생성
  const createParticles = useCallback((x: number, y: number, color: string, count = 8) => {
    for (let i = 0; i < count; i++) {
      const angle = (Math.PI * 2 * i) / count
      const speed = 2 + Math.random() * 3
      particlesRef.current.push({
        x,
        y,
        vx: Math.cos(angle) * speed,
        vy: Math.sin(angle) * speed,
        life: 30,
        maxLife: 30,
        color,
        size: 3 + Math.random() * 3,
      })
    }
  }, [])

  // 적 스폰
  const spawnEnemy = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const side = Math.floor(Math.random() * 4)
    let x, y

    switch (side) {
      case 0: // 위
        x = Math.random() * canvas.width
        y = -50
        break
      case 1: // 오른쪽
        x = canvas.width + 50
        y = Math.random() * canvas.height
        break
      case 2: // 아래
        x = Math.random() * canvas.width
        y = canvas.height + 50
        break
      default: // 왼쪽
        x = -50
        y = Math.random() * canvas.height
        break
    }

    const enemy: Enemy = {
      id: Date.now() + Math.random(),
      x,
      y,
      width: 30,
      height: 30,
      health: 2 + Math.floor(wave / 3),
      maxHealth: 2 + Math.floor(wave / 3),
      speed: 1 + Math.random() * 0.5 + wave * 0.1,
      isHit: false,
      hitTimer: 0,
      knockbackX: 0,
      knockbackY: 0,
    }

    enemiesRef.current.push(enemy)
  }, [wave])

  // 키보드 이벤트
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.key.toLowerCase())

      if (e.key === " " || e.key === "Spacebar") {
        e.preventDefault()
        if (characterRef.current.attackCooldown <= 0) {
          characterRef.current.isAttacking = true
          characterRef.current.attackCooldown = 20
          playSound(200, 0.1, "square")
        }
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.key.toLowerCase())
    }

    window.addEventListener("keydown", handleKeyDown)
    window.addEventListener("keyup", handleKeyUp)

    return () => {
      window.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("keyup", handleKeyUp)
    }
  }, [playSound])

  // 충돌 감지
  const checkCollision = useCallback((rect1: any, rect2: any) => {
    return (
      rect1.x < rect2.x + rect2.width &&
      rect1.x + rect1.width > rect2.x &&
      rect1.y < rect2.y + rect2.height &&
      rect1.y + rect1.height > rect2.y
    )
  }, [])

  // 게임 업데이트
  const updateGame = useCallback(
    (deltaTime: number) => {
      if (gameState !== "playing") return

      const canvas = canvasRef.current
      if (!canvas) return

      const character = characterRef.current
      const keys = keysRef.current

      // 캐릭터 이동
      const speed = 4
      let moved = false

      if (keys.has("w") || keys.has("arrowup")) {
        character.y = Math.max(0, character.y - speed)
        moved = true
      }
      if (keys.has("s") || keys.has("arrowdown")) {
        character.y = Math.min(canvas.height - character.height, character.y + speed)
        moved = true
      }
      if (keys.has("a") || keys.has("arrowleft")) {
        character.x = Math.max(0, character.x - speed)
        character.direction = 1
        moved = true
      }
      if (keys.has("d") || keys.has("arrowright")) {
        character.x = Math.min(canvas.width - character.width, character.x + speed)
        character.direction = 0
        moved = true
      }

      // 공격 쿨다운
      if (character.attackCooldown > 0) {
        character.attackCooldown--
      }
      if (character.isAttacking && character.attackCooldown <= 15) {
        character.isAttacking = false
      }

      // 적 업데이트
      enemiesRef.current.forEach((enemy, index) => {
        // 넉백 처리
        if (enemy.knockbackX !== 0 || enemy.knockbackY !== 0) {
          enemy.x += enemy.knockbackX
          enemy.y += enemy.knockbackY
          enemy.knockbackX *= 0.9
          enemy.knockbackY *= 0.9
          if (Math.abs(enemy.knockbackX) < 0.1) enemy.knockbackX = 0
          if (Math.abs(enemy.knockbackY) < 0.1) enemy.knockbackY = 0
        } else {
          // 캐릭터 추적
          const dx = character.x - enemy.x
          const dy = character.y - enemy.y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance > 0) {
            enemy.x += (dx / distance) * enemy.speed
            enemy.y += (dy / distance) * enemy.speed
          }
        }

        // 피격 타이머
        if (enemy.isHit) {
          enemy.hitTimer--
          if (enemy.hitTimer <= 0) {
            enemy.isHit = false
          }
        }

        // 캐릭터와 충돌 (데미지)
        if (checkCollision(character, enemy)) {
          character.health -= 0.5
          if (character.health <= 0) {
            setGameState("gameOver")
          }
        }

        // 공격 범위 체크
        if (character.isAttacking && character.attackCooldown > 15) {
          const attackRange = {
            x: character.direction === 0 ? character.x + character.width : character.x - 60,
            y: character.y - 10,
            width: 60,
            height: character.height + 20,
          }

          if (checkCollision(attackRange, enemy) && !enemy.isHit) {
            enemy.health--
            enemy.isHit = true
            enemy.hitTimer = 10

            // 넉백 효과
            const knockbackForce = 8
            const dx = enemy.x - character.x
            const dy = enemy.y - character.y
            const distance = Math.sqrt(dx * dx + dy * dy)

            if (distance > 0) {
              enemy.knockbackX = (dx / distance) * knockbackForce
              enemy.knockbackY = (dy / distance) * knockbackForce
            }

            createParticles(enemy.x + enemy.width / 2, enemy.y + enemy.height / 2, "#ff4444", 6)
            playSound(150, 0.15, "sawtooth")

            if (enemy.health <= 0) {
              enemiesRef.current.splice(index, 1)
              setScore((prev) => prev + 10)
              createParticles(enemy.x + enemy.width / 2, enemy.y + enemy.height / 2, "#ffff44", 12)
              playSound(300, 0.2, "sine")
            }
          }
        }
      })

      // 파티클 업데이트
      particlesRef.current = particlesRef.current.filter((particle) => {
        particle.x += particle.vx
        particle.y += particle.vy
        particle.vx *= 0.98
        particle.vy *= 0.98
        particle.life--
        return particle.life > 0
      })

      // 적 스폰
      if (Math.random() < 0.02 + wave * 0.005) {
        spawnEnemy()
      }

      // 웨이브 증가
      if (score > 0 && score % 100 === 0 && score / 100 > wave - 1) {
        setWave((prev) => prev + 1)
      }
    },
    [gameState, checkCollision, createParticles, playSound, spawnEnemy, score, wave],
  )

  // 렌더링
  const render = useCallback(() => {
    const canvas = canvasRef.current
    const ctx = canvas?.getContext("2d")
    if (!canvas || !ctx) return

    // 배경
    ctx.fillStyle = "#1a1a2e"
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 그리드 패턴
    ctx.strokeStyle = "#16213e"
    ctx.lineWidth = 1
    for (let x = 0; x < canvas.width; x += 40) {
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, canvas.height)
      ctx.stroke()
    }
    for (let y = 0; y < canvas.height; y += 40) {
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(canvas.width, y)
      ctx.stroke()
    }

    const character = characterRef.current

    // 공격 범위 표시
    if (character.isAttacking) {
      ctx.fillStyle = "rgba(255, 255, 0, 0.3)"
      const attackX = character.direction === 0 ? character.x + character.width : character.x - 60
      ctx.fillRect(attackX, character.y - 10, 60, character.height + 20)
    }

    // 캐릭터
    ctx.fillStyle = character.health < 30 ? "#ff6b6b" : "#4ecdc4"
    if (character.isAttacking) {
      ctx.fillStyle = "#ffe66d"
    }
    ctx.fillRect(character.x, character.y, character.width, character.height)

    // 캐릭터 방향 표시
    ctx.fillStyle = "#ffffff"
    const eyeX = character.direction === 0 ? character.x + 30 : character.x + 10
    ctx.fillRect(eyeX, character.y + 10, 6, 6)

    // 적들
    enemiesRef.current.forEach((enemy) => {
      ctx.fillStyle = enemy.isHit ? "#ffffff" : "#ff4757"
      ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height)

      // 적 체력바
      if (enemy.health < enemy.maxHealth) {
        ctx.fillStyle = "#333"
        ctx.fillRect(enemy.x, enemy.y - 8, enemy.width, 4)
        ctx.fillStyle = "#ff4757"
        ctx.fillRect(enemy.x, enemy.y - 8, (enemy.health / enemy.maxHealth) * enemy.width, 4)
      }
    })

    // 파티클
    particlesRef.current.forEach((particle) => {
      const alpha = particle.life / particle.maxLife
      ctx.fillStyle =
        particle.color +
        Math.floor(alpha * 255)
          .toString(16)
          .padStart(2, "0")
      ctx.fillRect(particle.x - particle.size / 2, particle.y - particle.size / 2, particle.size, particle.size)
    })

    // UI
    ctx.fillStyle = "#ffffff"
    ctx.font = "20px Arial"
    ctx.fillText(`체력: ${Math.max(0, Math.floor(character.health))}`, 20, 30)
    ctx.fillText(`점수: ${score}`, 20, 60)
    ctx.fillText(`웨이브: ${wave}`, 20, 90)
    ctx.fillText(`적: ${enemiesRef.current.length}`, 20, 120)

    // 조작법
    ctx.font = "14px Arial"
    ctx.fillStyle = "#aaa"
    ctx.fillText("WASD: 이동, 스페이스바: 공격", canvas.width - 200, canvas.height - 20)

    if (gameState === "gameOver") {
      ctx.fillStyle = "rgba(0, 0, 0, 0.8)"
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      ctx.fillStyle = "#ffffff"
      ctx.font = "48px Arial"
      ctx.textAlign = "center"
      ctx.fillText("게임 오버!", canvas.width / 2, canvas.height / 2 - 50)
      ctx.font = "24px Arial"
      ctx.fillText(`최종 점수: ${score}`, canvas.width / 2, canvas.height / 2)
      ctx.fillText(`도달 웨이브: ${wave}`, canvas.width / 2, canvas.height / 2 + 30)
      ctx.textAlign = "left"
    }
  }, [gameState, score, wave])

  // 게임 루프
  useEffect(() => {
    const gameLoop = (currentTime: number) => {
      const deltaTime = currentTime - lastTimeRef.current
      lastTimeRef.current = currentTime

      updateGame(deltaTime)
      render()

      animationRef.current = requestAnimationFrame(gameLoop)
    }

    animationRef.current = requestAnimationFrame(gameLoop)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [updateGame, render])

  const resetGame = () => {
    characterRef.current = {
      x: 400,
      y: 300,
      width: 40,
      height: 40,
      health: 100,
      maxHealth: 100,
      isAttacking: false,
      attackCooldown: 0,
      direction: 0,
    }
    enemiesRef.current = []
    particlesRef.current = []
    setScore(0)
    setWave(1)
    setGameState("playing")
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-900 p-4">
      <div className="mb-4">
        <h1 className="text-3xl font-bold text-white mb-2">헌터 게임</h1>
        <div className="flex gap-4">
          <Button
            onClick={resetGame}
            variant="outline"
            className="bg-blue-600 hover:bg-blue-700 text-white border-blue-600"
          >
            새 게임
          </Button>
        </div>
      </div>

      <canvas
        ref={canvasRef}
        width={800}
        height={600}
        className="border-2 border-gray-600 rounded-lg bg-gray-800"
        tabIndex={0}
      />

      <div className="mt-4 text-center text-gray-300">
        <p className="text-sm">WASD 또는 화살표 키로 이동, 스페이스바로 공격</p>
        <p className="text-xs mt-1">적들을 처치하고 최대한 오래 버텨보세요!</p>
      </div>
    </div>
  )
}
