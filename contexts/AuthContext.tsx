"use client";

import { auth, db } from "@/lib/firebase";
import {
  GoogleAuth<PERSON>rovider,
  User,
  createUserWithEmailAndPassword,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  signInWithPopup,
  signInWithRedirect,
  signOut,
} from "firebase/auth";
import { doc, getDoc, setDoc } from "firebase/firestore";
import React, { createContext, useContext, useEffect, useState } from "react";

interface AuthContextType {
  user: User | null;
  isGuest: boolean;
  guestName: string;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (
    email: string,
    password: string,
    displayName: string
  ) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInAsGuest: () => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isGuest, setIsGuest] = useState(false);
  const [guestName, setGuestName] = useState("");
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);

    // 게스트 세션 복원 확인
    const guestSession = localStorage.getItem("guestSession");
    if (guestSession) {
      try {
        const session = JSON.parse(guestSession);
        setIsGuest(true);
        setGuestName(session.name);
      } catch (error) {
        localStorage.removeItem("guestSession");
      }
    }
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });

    return unsubscribe;
  }, [mounted]);

  const signIn = async (email: string, password: string) => {
    const result = await signInWithEmailAndPassword(auth, email, password);
    return result;
  };

  const signUp = async (
    email: string,
    password: string,
    displayName: string
  ) => {
    const result = await createUserWithEmailAndPassword(auth, email, password);

    // 사용자 프로필 데이터 생성
    if (result.user) {
      await setDoc(doc(db, "users", result.user.uid), {
        email: result.user.email,
        displayName: displayName,
        createdAt: new Date(),
        gameData: {
          level: 1,
          exp: 0,
          expToNext: 100,
          health: 100,
          maxHealth: 100,
          mana: 50,
          maxMana: 50,
          gold: 50,
          stats: {
            strength: 10,
            agility: 8,
            vitality: 12,
            intelligence: 6,
          },
          inventory: [
            {
              id: "sword1",
              name: "철검",
              type: "weapon",
              quantity: 1,
              description: "튼튼한 철로 만든 검",
              icon: "⚔️",
              rarity: "common",
              stats: { attack: 15 },
            },
            {
              id: "potion1",
              name: "체력 물약",
              type: "potion",
              quantity: 3,
              description: "체력을 회복하는 물약",
              icon: "🧪",
              rarity: "common",
              stats: { health: 50 },
            },
            {
              id: "leather_armor",
              name: "가죽 갑옷",
              type: "armor",
              quantity: 1,
              description: "가벼운 가죽으로 만든 갑옷",
              icon: "🛡️",
              rarity: "common",
              stats: { defense: 8 },
            },
          ],
          equipped: {
            weapon: {
              id: "sword1",
              name: "철검",
              type: "weapon",
              quantity: 1,
              description: "튼튼한 철로 만든 검",
              icon: "⚔️",
              rarity: "common",
              stats: { attack: 15 },
            },
          },
          position: { x: 2400, y: 2400 }, // 월드 중앙
          lastSaved: new Date(),
        },
      });
    }

    return result;
  };

  const signInWithGoogle = async (useRedirect = false) => {
    const provider = new GoogleAuthProvider();

    if (useRedirect) {
      // 리디렉션 방식 (COOP 문제 해결용)
      await signInWithRedirect(auth, provider);
      return null; // 리디렉션 후에는 결과를 기다리지 않음
    }

    // 기본 팝업 방식
    const result = await signInWithPopup(auth, provider);

    // 신규 사용자인지 확인
    if (result.user) {
      const userDoc = await getDoc(doc(db, "users", result.user.uid));

      if (!userDoc.exists()) {
        // 신규 Google 사용자 - 기본 게임 데이터 생성
        await setDoc(doc(db, "users", result.user.uid), {
          email: result.user.email,
          displayName: result.user.displayName || "무명의 무사",
          createdAt: new Date(),
          gameData: {
            level: 1,
            exp: 0,
            expToNext: 100,
            health: 100,
            maxHealth: 100,
            mana: 50,
            maxMana: 50,
            gold: 50,
            stats: {
              strength: 10,
              agility: 8,
              vitality: 12,
              intelligence: 6,
            },
            inventory: [
              {
                id: "sword1",
                name: "철검",
                type: "weapon",
                quantity: 1,
                description: "튼튼한 철로 만든 검",
                icon: "⚔️",
                rarity: "common",
                stats: { attack: 15 },
              },
              {
                id: "potion1",
                name: "체력 물약",
                type: "potion",
                quantity: 3,
                description: "체력을 회복하는 물약",
                icon: "🧪",
                rarity: "common",
                stats: { health: 50 },
              },
              {
                id: "leather_armor",
                name: "가죽 갑옷",
                type: "armor",
                quantity: 1,
                description: "가벼운 가죽으로 만든 갑옷",
                icon: "🛡️",
                rarity: "common",
                stats: { defense: 8 },
              },
            ],
            equipped: {
              weapon: {
                id: "sword1",
                name: "철검",
                type: "weapon",
                quantity: 1,
                description: "튼튼한 철로 만든 검",
                icon: "⚔️",
                rarity: "common",
                stats: { attack: 15 },
              },
            },
            position: { x: 2400, y: 2400 },
            lastSaved: new Date(),
          },
        });
      }
    }

    return result;
  };

  // 게스트용 랜덤 이름 생성
  const generateGuestName = () => {
    const adjectives = [
      "용감한",
      "신속한",
      "현명한",
      "강인한",
      "영웅적인",
      "날쌘",
      "우아한",
      "대담한",
    ];
    const nouns = [
      "무사",
      "검객",
      "전사",
      "무인",
      "호걸",
      "영웅",
      "기사",
      "검사",
    ];
    const randomAdjective =
      adjectives[Math.floor(Math.random() * adjectives.length)];
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
    const randomNumber = Math.floor(Math.random() * 999) + 1;
    return `${randomAdjective}${randomNoun}${randomNumber}`;
  };

  const signInAsGuest = async () => {
    const guestDisplayName = generateGuestName();
    setIsGuest(true);
    setGuestName(guestDisplayName);
    setUser(null); // 게스트는 Firebase user가 없음

    // 로컬 스토리지에 게스트 정보 저장
    localStorage.setItem(
      "guestSession",
      JSON.stringify({
        name: guestDisplayName,
        loginTime: new Date().toISOString(),
      })
    );
  };

  const logout = async () => {
    if (isGuest) {
      // 게스트 로그아웃
      setIsGuest(false);
      setGuestName("");
      localStorage.removeItem("guestSession");
    } else {
      // 일반 사용자 로그아웃
      await signOut(auth);
    }
  };

  const value = {
    user,
    isGuest,
    guestName,
    loading: loading || !mounted,
    signIn,
    signUp,
    signInWithGoogle,
    signInAsGuest,
    logout,
  };

  if (!mounted) {
    return null; // SSR 호환성
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
