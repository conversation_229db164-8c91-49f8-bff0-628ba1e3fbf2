# 조선무사전 - Firebase 온라인 게임

## 🎮 Firebase 기능

### 🔐 인증 시스템

- 이메일/비밀번호 로그인
- Google 간편 로그인
- 자동 회원가입 시 기본 게임 데이터 생성

### ☁️ 클라우드 저장

- **자동 저장**: 30초마다 자동으로 게임 데이터 저장
- **레벨업 저장**: 레벨업 시 즉시 저장
- **랜덤 저장**: 적 처치 시 30% 확률로 저장 (성능 최적화)

### 📊 저장되는 데이터

- 캐릭터 레벨, 경험치
- 체력, 마나 상태
- 황금 보유량
- 스탯 (힘, 민첩, 체력, 지능)
- 인벤토리 아이템 목록
- 장착 중인 장비
- 캐릭터 위치

### 🚀 사용법

1. 게임 접속 후 로그인/회원가입
2. 자동으로 클라우드에서 저장된 데이터 로드
3. 게임 플레이 중 자동 저장
4. 다른 기기에서도 동일한 계정으로 이어서 플레이 가능

## 🔧 기술 스택

- **Frontend**: Next.js 14, React, TypeScript
- **Backend**: Firebase Authentication, Firestore
- **UI**: Tailwind CSS, Custom Canvas Rendering

## 📋 Firestore 데이터 구조

```javascript
users/{uid} = {
  email: string,
  displayName: string,
  createdAt: Date,
  gameData: {
    level: number,
    exp: number,
    expToNext: number,
    health: number,
    maxHealth: number,
    mana: number,
    maxMana: number,
    gold: number,
    stats: {
      strength: number,
      agility: number,
      vitality: number,
      intelligence: number
    },
    inventory: InventoryItem[],
    equipped: EquippedItems,
    position: { x: number, y: number },
    lastSaved: Date
  }
}
```
