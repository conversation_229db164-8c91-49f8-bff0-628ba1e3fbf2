"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Button } from "@/components/ui/button"

interface Vector2 {
  x: number
  y: number
}

interface Character {
  pos: Vector2
  vel: Vector2
  size: Vector2
  health: number
  maxHealth: number
  state: "idle" | "running" | "attacking" | "dashing"
  direction: number // -1 left, 1 right
  animFrame: number
  animTimer: number
  attackTimer: number
  dashTimer: number
  dashCooldown: number
  invulnerable: number
}

interface Enemy {
  id: number
  pos: Vector2
  vel: Vector2
  size: Vector2
  health: number
  maxHealth: number
  state: "idle" | "chasing" | "hit" | "dying"
  direction: number
  animFrame: number
  animTimer: number
  hitTimer: number
  deathTimer: number
  knockback: Vector2
}

interface Particle {
  pos: Vector2
  vel: Vector2
  life: number
  maxLife: number
  color: string
  size: number
  type: "spark" | "blood" | "slash"
  rotation: number
  rotSpeed: number
}

interface SlashEffect {
  pos: Vector2
  rotation: number
  scale: number
  life: number
  maxLife: number
}

export default function ActionGame() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>()
  const keysRef = useRef<Set<string>>(new Set())
  const lastTimeRef = useRef<number>(0)

  const [gameState, setGameState] = useState<"playing" | "paused" | "gameOver">("playing")
  const [score, setScore] = useState(0)
  const [combo, setCombo] = useState(0)
  const [comboTimer, setComboTimer] = useState(0)

  // 화면 흔들림
  const screenShakeRef = useRef({ x: 0, y: 0, intensity: 0, duration: 0 })

  // 시간 정지 효과
  const timeStopRef = useRef({ duration: 0, intensity: 1 })

  const characterRef = useRef<Character>({
    pos: { x: 400, y: 300 },
    vel: { x: 0, y: 0 },
    size: { x: 32, y: 32 },
    health: 100,
    maxHealth: 100,
    state: "idle",
    direction: 1,
    animFrame: 0,
    animTimer: 0,
    attackTimer: 0,
    dashTimer: 0,
    dashCooldown: 0,
    invulnerable: 0,
  })

  const enemiesRef = useRef<Enemy[]>([])
  const particlesRef = useRef<Particle[]>([])
  const slashEffectsRef = useRef<SlashEffect[]>([])

  // 고품질 사운드 효과
  const playSound = useCallback(
    (frequency: number, duration: number, type: "sine" | "square" | "sawtooth" = "sine", volume = 0.1) => {
      try {
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        const oscillator = audioContext.createOscillator()
        const gainNode = audioContext.createGain()
        const filter = audioContext.createBiquadFilter()

        oscillator.connect(filter)
        filter.connect(gainNode)
        gainNode.connect(audioContext.destination)

        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime)
        oscillator.type = type

        filter.type = "lowpass"
        filter.frequency.setValueAtTime(frequency * 2, audioContext.currentTime)

        gainNode.gain.setValueAtTime(volume, audioContext.currentTime)
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration)

        oscillator.start(audioContext.currentTime)
        oscillator.stop(audioContext.currentTime + duration)
      } catch (e) {
        // 사운드 실패 시 무시
      }
    },
    [],
  )

  // 화면 흔들림 효과
  const addScreenShake = useCallback((intensity: number, duration: number) => {
    screenShakeRef.current = { x: 0, y: 0, intensity, duration }
  }, [])

  // 시간 정지 효과
  const addTimeStop = useCallback((duration: number, intensity = 0.1) => {
    timeStopRef.current = { duration, intensity }
  }, [])

  // 파티클 생성
  const createParticles = useCallback(
    (pos: Vector2, type: "spark" | "blood" | "slash", count = 8, color = "#ff4444") => {
      for (let i = 0; i < count; i++) {
        const angle = (Math.PI * 2 * i) / count + (Math.random() - 0.5) * 0.5
        const speed = 3 + Math.random() * 5

        particlesRef.current.push({
          pos: { x: pos.x, y: pos.y },
          vel: {
            x: Math.cos(angle) * speed,
            y: Math.sin(angle) * speed,
          },
          life: type === "slash" ? 15 : 30,
          maxLife: type === "slash" ? 15 : 30,
          color,
          size: type === "slash" ? 8 : 4,
          type,
          rotation: angle,
          rotSpeed: (Math.random() - 0.5) * 0.3,
        })
      }
    },
    [],
  )

  // 슬래시 이펙트 생성
  const createSlashEffect = useCallback((pos: Vector2, direction: number) => {
    slashEffectsRef.current.push({
      pos: { x: pos.x, y: pos.y },
      rotation: direction > 0 ? 0 : Math.PI,
      scale: 0,
      life: 20,
      maxLife: 20,
    })
  }, [])

  // 적 스폰
  const spawnEnemy = useCallback(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const side = Math.floor(Math.random() * 4)
    let x, y

    switch (side) {
      case 0:
        x = Math.random() * canvas.width
        y = -50
        break
      case 1:
        x = canvas.width + 50
        y = Math.random() * canvas.height
        break
      case 2:
        x = Math.random() * canvas.width
        y = canvas.height + 50
        break
      default:
        x = -50
        y = Math.random() * canvas.height
        break
    }

    enemiesRef.current.push({
      id: Date.now() + Math.random(),
      pos: { x, y },
      vel: { x: 0, y: 0 },
      size: { x: 28, y: 28 },
      health: 3,
      maxHealth: 3,
      state: "chasing",
      direction: 1,
      animFrame: 0,
      animTimer: 0,
      hitTimer: 0,
      deathTimer: 0,
      knockback: { x: 0, y: 0 },
    })
  }, [])

  // 키보드 입력
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      keysRef.current.add(e.key.toLowerCase())

      const character = characterRef.current

      if (e.key === " " || e.key === "Spacebar") {
        e.preventDefault()
        if (character.state !== "attacking" && character.attackTimer <= 0) {
          character.state = "attacking"
          character.attackTimer = 25
          createSlashEffect(
            { x: character.pos.x + character.size.x / 2, y: character.pos.y + character.size.y / 2 },
            character.direction,
          )
          playSound(300, 0.15, "sawtooth", 0.15)
        }
      }

      if ((e.key === "shift" || e.key === "Shift") && character.dashCooldown <= 0) {
        character.state = "dashing"
        character.dashTimer = 15
        character.dashCooldown = 60
        character.invulnerable = 15
        playSound(200, 0.1, "square", 0.1)
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      keysRef.current.delete(e.key.toLowerCase())
    }

    window.addEventListener("keydown", handleKeyDown)
    window.addEventListener("keyup", handleKeyUp)

    return () => {
      window.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("keyup", handleKeyUp)
    }
  }, [createSlashEffect, playSound])

  // 충돌 감지
  const checkCollision = useCallback((a: any, b: any) => {
    return (
      a.pos.x < b.pos.x + b.size.x &&
      a.pos.x + a.size.x > b.pos.x &&
      a.pos.y < b.pos.y + b.size.y &&
      a.pos.y + a.size.y > b.pos.y
    )
  }, [])

  // 거리 계산
  const distance = useCallback((a: Vector2, b: Vector2) => {
    const dx = a.x - b.x
    const dy = a.y - b.y
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  // 게임 업데이트
  const updateGame = useCallback(
    (deltaTime: number) => {
      if (gameState !== "playing") return

      const canvas = canvasRef.current
      if (!canvas) return

      // 시간 정지 효과 적용
      const timeScale = timeStopRef.current.duration > 0 ? timeStopRef.current.intensity : 1
      if (timeStopRef.current.duration > 0) {
        timeStopRef.current.duration--
      }

      const character = characterRef.current
      const keys = keysRef.current

      // 캐릭터 업데이트
      character.vel.x *= 0.8
      character.vel.y *= 0.8

      // 이동 입력
      const speed = character.state === "dashing" ? 12 : 5
      let moving = false

      if (keys.has("a") || keys.has("arrowleft")) {
        character.vel.x = -speed
        character.direction = -1
        moving = true
      }
      if (keys.has("d") || keys.has("arrowright")) {
        character.vel.x = speed
        character.direction = 1
        moving = true
      }
      if (keys.has("w") || keys.has("arrowup")) {
        character.vel.y = -speed
        moving = true
      }
      if (keys.has("s") || keys.has("arrowdown")) {
        character.vel.y = speed
        moving = true
      }

      // 상태 업데이트
      if (character.attackTimer > 0) {
        character.attackTimer--
        if (character.attackTimer <= 0) {
          character.state = "idle"
        }
      }

      if (character.dashTimer > 0) {
        character.dashTimer--
        if (character.dashTimer <= 0) {
          character.state = "idle"
        }
      }

      if (character.dashCooldown > 0) character.dashCooldown--
      if (character.invulnerable > 0) character.invulnerable--

      if (character.state === "idle" && moving) {
        character.state = "running"
      } else if (character.state === "running" && !moving) {
        character.state = "idle"
      }

      // 위치 업데이트
      character.pos.x += character.vel.x * timeScale
      character.pos.y += character.vel.y * timeScale

      // 화면 경계
      character.pos.x = Math.max(0, Math.min(canvas.width - character.size.x, character.pos.x))
      character.pos.y = Math.max(0, Math.min(canvas.height - character.size.y, character.pos.y))

      // 애니메이션
      character.animTimer++
      if (character.animTimer > 8) {
        character.animFrame = (character.animFrame + 1) % 4
        character.animTimer = 0
      }

      // 적 업데이트
      enemiesRef.current.forEach((enemy, index) => {
        if (enemy.state === "dying") {
          enemy.deathTimer++
          if (enemy.deathTimer > 30) {
            enemiesRef.current.splice(index, 1)
          }
          return
        }

        // 넉백 처리
        if (enemy.knockback.x !== 0 || enemy.knockback.y !== 0) {
          enemy.pos.x += enemy.knockback.x * timeScale
          enemy.pos.y += enemy.knockback.y * timeScale
          enemy.knockback.x *= 0.9
          enemy.knockback.y *= 0.9
          if (Math.abs(enemy.knockback.x) < 0.1) enemy.knockback.x = 0
          if (Math.abs(enemy.knockback.y) < 0.1) enemy.knockback.y = 0
        } else if (enemy.state === "chasing") {
          // 캐릭터 추적
          const dist = distance(enemy.pos, character.pos)
          if (dist > 0) {
            const dx = (character.pos.x - enemy.pos.x) / dist
            const dy = (character.pos.y - enemy.pos.y) / dist
            const speed = 2

            enemy.vel.x = dx * speed
            enemy.vel.y = dy * speed
            enemy.direction = dx > 0 ? 1 : -1
          }
        }

        enemy.pos.x += enemy.vel.x * timeScale
        enemy.pos.y += enemy.vel.y * timeScale

        // 피격 상태 처리
        if (enemy.state === "hit") {
          enemy.hitTimer--
          if (enemy.hitTimer <= 0) {
            enemy.state = "chasing"
          }
        }

        // 캐릭터와 충돌 (데미지)
        if (checkCollision(character, enemy) && character.invulnerable <= 0) {
          character.health -= 2
          character.invulnerable = 60
          addScreenShake(8, 20)
          playSound(150, 0.2, "square", 0.2)

          if (character.health <= 0) {
            setGameState("gameOver")
          }
        }

        // 공격 범위 체크
        if (character.state === "attacking" && character.attackTimer > 15) {
          const attackRange = {
            pos: {
              x: character.pos.x + (character.direction > 0 ? character.size.x : -40),
              y: character.pos.y - 10,
            },
            size: { x: 50, y: character.size.y + 20 },
          }

          if (checkCollision(attackRange, enemy) && enemy.state !== "hit" && enemy.state !== "dying") {
            enemy.health--
            enemy.state = "hit"
            enemy.hitTimer = 15

            // 강력한 넉백
            const knockbackForce = 15
            const dx = enemy.pos.x - character.pos.x
            const dy = enemy.pos.y - character.pos.y
            const dist = Math.sqrt(dx * dx + dy * dy)

            if (dist > 0) {
              enemy.knockback.x = (dx / dist) * knockbackForce
              enemy.knockback.y = (dy / dist) * knockbackForce
            }

            // 타격 효과
            createParticles(
              { x: enemy.pos.x + enemy.size.x / 2, y: enemy.pos.y + enemy.size.y / 2 },
              "blood",
              12,
              "#ff2244",
            )

            addScreenShake(6, 15)
            addTimeStop(8, 0.3)
            playSound(250, 0.2, "sawtooth", 0.2)

            // 콤보 증가
            setCombo((prev) => prev + 1)
            setComboTimer(180)

            if (enemy.health <= 0) {
              enemy.state = "dying"
              enemy.deathTimer = 0
              setScore((prev) => prev + 10 * (combo + 1))

              createParticles(
                { x: enemy.pos.x + enemy.size.x / 2, y: enemy.pos.y + enemy.size.y / 2 },
                "spark",
                20,
                "#ffff44",
              )

              addScreenShake(10, 25)
              addTimeStop(12, 0.2)
              playSound(400, 0.3, "sine", 0.25)
            }
          }
        }

        // 애니메이션
        enemy.animTimer++
        if (enemy.animTimer > 10) {
          enemy.animFrame = (enemy.animFrame + 1) % 3
          enemy.animTimer = 0
        }
      })

      // 콤보 타이머
      if (comboTimer > 0) {
        setComboTimer((prev) => prev - 1)
        if (comboTimer === 1) {
          setCombo(0)
        }
      }

      // 파티클 업데이트
      particlesRef.current = particlesRef.current.filter((particle) => {
        particle.pos.x += particle.vel.x * timeScale
        particle.pos.y += particle.vel.y * timeScale
        particle.vel.x *= 0.95
        particle.vel.y *= 0.95
        particle.rotation += particle.rotSpeed
        particle.life--
        return particle.life > 0
      })

      // 슬래시 이펙트 업데이트
      slashEffectsRef.current = slashEffectsRef.current.filter((slash) => {
        slash.life--
        slash.scale = Math.min(1, (slash.maxLife - slash.life) / 5)
        return slash.life > 0
      })

      // 화면 흔들림 업데이트
      if (screenShakeRef.current.duration > 0) {
        screenShakeRef.current.x = (Math.random() - 0.5) * screenShakeRef.current.intensity
        screenShakeRef.current.y = (Math.random() - 0.5) * screenShakeRef.current.intensity
        screenShakeRef.current.duration--
      } else {
        screenShakeRef.current.x = 0
        screenShakeRef.current.y = 0
      }

      // 적 스폰
      if (Math.random() < 0.015) {
        spawnEnemy()
      }
    },
    [
      gameState,
      checkCollision,
      distance,
      createParticles,
      addScreenShake,
      addTimeStop,
      playSound,
      spawnEnemy,
      combo,
      comboTimer,
    ],
  )

  // 렌더링
  const render = useCallback(() => {
    const canvas = canvasRef.current
    const ctx = canvas?.getContext("2d")
    if (!canvas || !ctx) return

    // 화면 흔들림 적용
    ctx.save()
    ctx.translate(screenShakeRef.current.x, screenShakeRef.current.y)

    // 배경
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
    gradient.addColorStop(0, "#0f0f23")
    gradient.addColorStop(1, "#1a1a2e")
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 그리드
    ctx.strokeStyle = "rgba(255, 255, 255, 0.05)"
    ctx.lineWidth = 1
    for (let x = 0; x < canvas.width; x += 50) {
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, canvas.height)
      ctx.stroke()
    }
    for (let y = 0; y < canvas.height; y += 50) {
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(canvas.width, y)
      ctx.stroke()
    }

    const character = characterRef.current

    // 슬래시 이펙트
    slashEffectsRef.current.forEach((slash) => {
      ctx.save()
      ctx.translate(slash.pos.x, slash.pos.y)
      ctx.rotate(slash.rotation)
      ctx.scale(slash.scale, slash.scale)

      const alpha = slash.life / slash.maxLife
      ctx.strokeStyle = `rgba(255, 255, 100, ${alpha})`
      ctx.lineWidth = 8
      ctx.lineCap = "round"

      ctx.beginPath()
      ctx.arc(0, 0, 30, -0.5, 0.5)
      ctx.stroke()

      ctx.restore()
    })

    // 캐릭터
    ctx.save()
    ctx.translate(character.pos.x + character.size.x / 2, character.pos.y + character.size.y / 2)
    if (character.direction < 0) ctx.scale(-1, 1)

    // 캐릭터 색상
    let characterColor = "#4ecdc4"
    if (character.invulnerable > 0 && Math.floor(character.invulnerable / 5) % 2) {
      characterColor = "rgba(78, 205, 196, 0.5)"
    }
    if (character.state === "attacking") {
      characterColor = "#ffe66d"
    }
    if (character.state === "dashing") {
      characterColor = "#ff6b6b"
    }

    ctx.fillStyle = characterColor
    ctx.fillRect(-character.size.x / 2, -character.size.y / 2, character.size.x, character.size.y)

    // 캐릭터 디테일
    ctx.fillStyle = "#ffffff"
    ctx.fillRect(8, -12, 6, 6) // 눈

    if (character.state === "attacking") {
      // 검 그리기
      ctx.strokeStyle = "#ffffff"
      ctx.lineWidth = 3
      ctx.lineCap = "round"
      ctx.beginPath()
      ctx.moveTo(20, 0)
      ctx.lineTo(35, -5)
      ctx.stroke()
    }

    ctx.restore()

    // 적들
    enemiesRef.current.forEach((enemy) => {
      ctx.save()
      ctx.translate(enemy.pos.x + enemy.size.x / 2, enemy.pos.y + enemy.size.y / 2)
      if (enemy.direction < 0) ctx.scale(-1, 1)

      let enemyColor = "#ff4757"
      if (enemy.state === "hit") {
        enemyColor = "#ffffff"
      }
      if (enemy.state === "dying") {
        const alpha = 1 - enemy.deathTimer / 30
        enemyColor = `rgba(255, 71, 87, ${alpha})`
      }

      ctx.fillStyle = enemyColor
      ctx.fillRect(-enemy.size.x / 2, -enemy.size.y / 2, enemy.size.x, enemy.size.y)

      // 적 눈
      if (enemy.state !== "dying") {
        ctx.fillStyle = "#ffffff"
        ctx.fillRect(6, -8, 4, 4)
      }

      ctx.restore()

      // 체력바
      if (enemy.health < enemy.maxHealth && enemy.state !== "dying") {
        ctx.fillStyle = "rgba(0, 0, 0, 0.5)"
        ctx.fillRect(enemy.pos.x, enemy.pos.y - 8, enemy.size.x, 4)
        ctx.fillStyle = "#ff4757"
        ctx.fillRect(enemy.pos.x, enemy.pos.y - 8, (enemy.health / enemy.maxHealth) * enemy.size.x, 4)
      }
    })

    // 파티클
    particlesRef.current.forEach((particle) => {
      const alpha = particle.life / particle.maxLife
      ctx.save()
      ctx.translate(particle.pos.x, particle.pos.y)
      ctx.rotate(particle.rotation)

      if (particle.type === "slash") {
        ctx.strokeStyle =
          particle.color +
          Math.floor(alpha * 255)
            .toString(16)
            .padStart(2, "0")
        ctx.lineWidth = particle.size
        ctx.lineCap = "round"
        ctx.beginPath()
        ctx.moveTo(-particle.size, 0)
        ctx.lineTo(particle.size, 0)
        ctx.stroke()
      } else {
        ctx.fillStyle =
          particle.color +
          Math.floor(alpha * 255)
            .toString(16)
            .padStart(2, "0")
        ctx.fillRect(-particle.size / 2, -particle.size / 2, particle.size, particle.size)
      }

      ctx.restore()
    })

    ctx.restore() // 화면 흔들림 복원

    // UI
    ctx.fillStyle = "#ffffff"
    ctx.font = "bold 24px Arial"
    ctx.fillText(`체력: ${Math.max(0, character.health)}`, 20, 40)
    ctx.fillText(`점수: ${score}`, 20, 70)

    if (combo > 0) {
      ctx.fillStyle = "#ffff44"
      ctx.font = "bold 32px Arial"
      ctx.fillText(`${combo}x COMBO!`, 20, 110)
    }

    // 쿨다운 표시
    if (character.dashCooldown > 0) {
      ctx.fillStyle = "rgba(255, 255, 255, 0.5)"
      ctx.fillRect(canvas.width - 120, 20, 100, 10)
      ctx.fillStyle = "#4ecdc4"
      ctx.fillRect(canvas.width - 120, 20, (1 - character.dashCooldown / 60) * 100, 10)
      ctx.fillStyle = "#ffffff"
      ctx.font = "12px Arial"
      ctx.fillText("DASH", canvas.width - 115, 45)
    }

    // 조작법
    ctx.fillStyle = "rgba(255, 255, 255, 0.7)"
    ctx.font = "14px Arial"
    ctx.fillText("WASD: 이동 | SPACE: 공격 | SHIFT: 대시", canvas.width - 300, canvas.height - 20)

    // 게임 오버
    if (gameState === "gameOver") {
      ctx.fillStyle = "rgba(0, 0, 0, 0.8)"
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      ctx.fillStyle = "#ffffff"
      ctx.font = "bold 48px Arial"
      ctx.textAlign = "center"
      ctx.fillText("게임 오버!", canvas.width / 2, canvas.height / 2 - 50)
      ctx.font = "24px Arial"
      ctx.fillText(`최종 점수: ${score}`, canvas.width / 2, canvas.height / 2)
      ctx.fillText(`최고 콤보: ${combo}`, canvas.width / 2, canvas.height / 2 + 30)
      ctx.textAlign = "left"
    }
  }, [gameState, score, combo])

  // 게임 루프
  useEffect(() => {
    const gameLoop = (currentTime: number) => {
      const deltaTime = currentTime - lastTimeRef.current
      lastTimeRef.current = currentTime

      updateGame(deltaTime)
      render()

      animationRef.current = requestAnimationFrame(gameLoop)
    }

    animationRef.current = requestAnimationFrame(gameLoop)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [updateGame, render])

  const resetGame = () => {
    characterRef.current = {
      pos: { x: 400, y: 300 },
      vel: { x: 0, y: 0 },
      size: { x: 32, y: 32 },
      health: 100,
      maxHealth: 100,
      state: "idle",
      direction: 1,
      animFrame: 0,
      animTimer: 0,
      attackTimer: 0,
      dashTimer: 0,
      dashCooldown: 0,
      invulnerable: 0,
    }
    enemiesRef.current = []
    particlesRef.current = []
    slashEffectsRef.current = []
    setScore(0)
    setCombo(0)
    setComboTimer(0)
    setGameState("playing")
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-900 p-4">
      <div className="mb-4">
        <h1 className="text-4xl font-bold text-white mb-2 text-center">⚔️ 액션 헌터 ⚔️</h1>
        <div className="flex gap-4 justify-center">
          <Button onClick={resetGame} className="bg-red-600 hover:bg-red-700 text-white font-bold px-6 py-2">
            새 게임
          </Button>
        </div>
      </div>

      <canvas
        ref={canvasRef}
        width={800}
        height={600}
        className="border-4 border-gray-600 rounded-lg bg-gray-800 shadow-2xl"
        tabIndex={0}
      />

      <div className="mt-4 text-center text-gray-300">
        <p className="text-lg font-semibold">🎮 WASD: 이동 | SPACE: 검 공격 | SHIFT: 대시</p>
        <p className="text-sm mt-1">연속 공격으로 콤보를 쌓아보세요! 대시로 적의 공격을 피하세요!</p>
      </div>
    </div>
  )
}
